"use strict";
exports.ja = void 0;
var _index = require("./ja/_lib/formatDistance.cjs");
var _index2 = require("./ja/_lib/formatLong.cjs");
var _index3 = require("./ja/_lib/formatRelative.cjs");
var _index4 = require("./ja/_lib/localize.cjs");
var _index5 = require("./ja/_lib/match.cjs");

/**
 * @category Locales
 * @summary Japanese locale.
 * @language Japanese
 * @iso-639-2 jpn
 * <AUTHOR> [@DeMuu](https://github.com/DeMuu)
 * <AUTHOR> [@ykzts](https://github.com/ykzts)
 * <AUTHOR> [@mesqueeb](https://github.com/mesqueeb)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)
 */
const ja = (exports.ja = {
  code: "ja",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
