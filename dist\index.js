"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManager = void 0;
class UserManager {
    constructor() {
        this.users = [];
    }
    addUser(user) {
        this.users.push(user);
        console.log(`User ${user.name} added successfully!`);
    }
    getUser(id) {
        return this.users.find(user => user.id === id);
    }
    getAllUsers() {
        return [...this.users];
    }
    removeUser(id) {
        const index = this.users.findIndex(user => user.id === id);
        if (index !== -1) {
            const removedUser = this.users.splice(index, 1)[0];
            console.log(`User ${removedUser?.name} removed successfully!`);
            return true;
        }
        return false;
    }
}
exports.UserManager = UserManager;
function main() {
    console.log('🚀 Welcome to the Simple TypeScript Project!');
    const userManager = new UserManager();
    userManager.addUser({ id: 1, name: '<PERSON>', email: '<EMAIL>' });
    userManager.addUser({ id: 2, name: '<PERSON>', email: '<EMAIL>' });
    console.log('\n📋 All Users:');
    console.log(userManager.getAllUsers());
    const user = userManager.getUser(1);
    console.log('\n🔍 Found User:', user);
    userManager.removeUser(2);
    console.log('\n📋 Remaining Users:');
    console.log(userManager.getAllUsers());
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=index.js.map