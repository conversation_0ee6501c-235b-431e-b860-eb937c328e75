{"name": "sort-object-keys", "version": "1.1.3", "description": "Sort an object's keys, including an optional key list", "keywords": ["keys", "object", "sort"], "homepage": "https://github.com/keithamus/sort-object-keys#readme", "bugs": {"url": "https://github.com/keithamus/sort-object-keys/issues"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://keithcirkel.co.uk/)", "files": ["index.js"], "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/keithamus/sort-object-keys.git"}, "scripts": {"semantic-release": "travis-after-all && semantic-release pre && npm publish && semantic-release post", "test": "node test.js"}, "config": {"ghooks": {"pre-commit": "npm t", "commit-msg": "validate-commit-msg"}}, "devDependencies": {"ghooks": "^1.0.1", "semantic-release": "^4.3.5", "travis-after-all": "^1.4.4", "validate-commit-msg": "^2.4.1"}}