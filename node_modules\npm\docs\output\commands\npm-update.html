<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-update</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-update----1093">
    <span>npm-update</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">Update packages</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#example">Example</a></li><ul><li><a href="#caret-dependencies">Caret Dependencies</a></li><li><a href="#tilde-dependencies">Tilde Dependencies</a></li><li><a href="#caret-dependencies-below-100">Caret Dependencies below 1.0.0</a></li><li><a href="#subdependencies">Subdependencies</a></li><li><a href="#updating-globally-installed-packages">Updating Globally-Installed Packages</a></li></ul><li><a href="#configuration">Configuration</a></li><ul><li><a href="#save"><code>save</code></a></li><li><a href="#global"><code>global</code></a></li><li><a href="#install-strategy"><code>install-strategy</code></a></li><li><a href="#legacy-bundling"><code>legacy-bundling</code></a></li><li><a href="#global-style"><code>global-style</code></a></li><li><a href="#omit"><code>omit</code></a></li><li><a href="#include"><code>include</code></a></li><li><a href="#strict-peer-deps"><code>strict-peer-deps</code></a></li><li><a href="#package-lock"><code>package-lock</code></a></li><li><a href="#foreground-scripts"><code>foreground-scripts</code></a></li><li><a href="#ignore-scripts"><code>ignore-scripts</code></a></li><li><a href="#audit"><code>audit</code></a></li><li><a href="#bin-links"><code>bin-links</code></a></li><li><a href="#fund"><code>fund</code></a></li><li><a href="#dry-run"><code>dry-run</code></a></li><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li><li><a href="#install-links"><code>install-links</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm update [&lt;pkg&gt;...]

aliases: up, upgrade, udpate
</code></pre>
<h3 id="description">Description</h3>
<p>This command will update all the packages listed to the latest version
(specified by the <a href="../using-npm/config#tag.html"><code>tag</code> config</a>), respecting the semver
constraints of both your package and its dependencies (if they also require the
same package).</p>
<p>It will also install missing packages.</p>
<p>If the <code>-g</code> flag is specified, this command will update globally installed
packages.</p>
<p>If no package name is specified, all packages in the specified location (global
or local) will be updated.</p>
<p>Note that by default <code>npm update</code> will not update the semver values of direct
dependencies in your project <code>package.json</code>. If you want to also update
values in <code>package.json</code> you can run: <code>npm update --save</code> (or add the
<code>save=true</code> option to a <a href="../configuring-npm/npmrc.html">configuration file</a>
to make that the default behavior).</p>
<h3 id="example">Example</h3>
<p>For the examples below, assume that the current package is <code>app</code> and it depends
on dependencies, <code>dep1</code> (<code>dep2</code>, .. etc.).  The published versions of <code>dep1</code>
are:</p>
<pre><code class="language-json">{
  "dist-tags": { "latest": "1.2.2" },
  "versions": [
    "1.2.2",
    "1.2.1",
    "1.2.0",
    "1.1.2",
    "1.1.1",
    "1.0.0",
    "0.4.1",
    "0.4.0",
    "0.2.0"
  ]
}
</code></pre>
<h4 id="caret-dependencies">Caret Dependencies</h4>
<p>If <code>app</code>'s <code>package.json</code> contains:</p>
<pre><code class="language-json">"dependencies": {
  "dep1": "^1.1.1"
}
</code></pre>
<p>Then <code>npm update</code> will install <code>dep1@1.2.2</code>, because <code>1.2.2</code> is <code>latest</code> and
<code>1.2.2</code> satisfies <code>^1.1.1</code>.</p>
<h4 id="tilde-dependencies">Tilde Dependencies</h4>
<p>However, if <code>app</code>'s <code>package.json</code> contains:</p>
<pre><code class="language-json">"dependencies": {
  "dep1": "~1.1.1"
}
</code></pre>
<p>In this case, running <code>npm update</code> will install <code>dep1@1.1.2</code>.  Even though the
<code>latest</code> tag points to <code>1.2.2</code>, this version does not satisfy <code>~1.1.1</code>, which is
equivalent to <code>&gt;=1.1.1 &lt;1.2.0</code>.  So the highest-sorting version that satisfies
<code>~1.1.1</code> is used, which is <code>1.1.2</code>.</p>
<h4 id="caret-dependencies-below-100">Caret Dependencies below 1.0.0</h4>
<p>Suppose <code>app</code> has a caret dependency on a version below <code>1.0.0</code>, for example:</p>
<pre><code class="language-json">"dependencies": {
  "dep1": "^0.2.0"
}
</code></pre>
<p><code>npm update</code> will install <code>dep1@0.2.0</code>.</p>
<p>If the dependence were on <code>^0.4.0</code>:</p>
<pre><code class="language-json">"dependencies": {
  "dep1": "^0.4.0"
}
</code></pre>
<p>Then <code>npm update</code> will install <code>dep1@0.4.1</code>, because that is the highest-sorting
version that satisfies <code>^0.4.0</code> (<code>&gt;= 0.4.0 &lt;0.5.0</code>)</p>
<h4 id="subdependencies">Subdependencies</h4>
<p>Suppose your app now also has a dependency on <code>dep2</code></p>
<pre><code class="language-json">{
  "name": "my-app",
  "dependencies": {
      "dep1": "^1.0.0",
      "dep2": "1.0.0"
  }
}
</code></pre>
<p>and <code>dep2</code> itself depends on this limited range of <code>dep1</code></p>
<pre><code class="language-json">{
"name": "dep2",
  "dependencies": {
    "dep1": "~1.1.1"
  }
}
</code></pre>
<p>Then <code>npm update</code> will install <code>dep1@1.1.2</code> because that is the highest
version that <code>dep2</code> allows.  npm will prioritize having a single version
of <code>dep1</code> in your tree rather than two when that single version can
satisfy the semver requirements of multiple dependencies in your tree.
In this case if you really did need your package to use a newer version
you would need to use <code>npm install</code>.</p>
<h4 id="updating-globally-installed-packages">Updating Globally-Installed Packages</h4>
<p><code>npm update -g</code> will apply the <code>update</code> action to each globally installed
package that is <code>outdated</code> -- that is, has a version that is different from
<code>wanted</code>.</p>
<p>Note: Globally installed packages are treated as if they are installed with a
caret semver range specified. So if you require to update to <code>latest</code> you may
need to run <code>npm install -g [&lt;pkg&gt;...]</code></p>
<p>NOTE: If a package has been upgraded to a version newer than <code>latest</code>, it will
be <em>downgraded</em>.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="save"><code>save</code></h4>
<ul>
<li>Default: <code>true</code> unless when using <code>npm update</code> where it defaults to <code>false</code></li>
<li>Type: Boolean</li>
</ul>
<p>Save installed packages to a <code>package.json</code> file as dependencies.</p>
<p>When used with the <code>npm rm</code> command, removes the dependency from
<code>package.json</code>.</p>
<p>Will also prevent writing to <code>package-lock.json</code> if set to <code>false</code>.</p>
<h4 id="global"><code>global</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Operates in "global" mode, so that packages are installed into the <code>prefix</code>
folder instead of the current working directory. See
<a href="../configuring-npm/folders.html">folders</a> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead
of the current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<h4 id="install-strategy"><code>install-strategy</code></h4>
<ul>
<li>Default: "hoisted"</li>
<li>Type: "hoisted", "nested", "shallow", or "linked"</li>
</ul>
<p>Sets the strategy for installing packages in node_modules. hoisted
(default): Install non-duplicated in top-level, and duplicated as necessary
within directory structure. nested: (formerly --legacy-bundling) install in
place, no hoisting. shallow (formerly --global-style) only install direct
deps at top-level. linked: (experimental) install in node_modules/.store,
link in place, unhoisted.</p>
<h4 id="legacy-bundling"><code>legacy-bundling</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
<li>DEPRECATED: This option has been deprecated in favor of
<code>--install-strategy=nested</code></li>
</ul>
<p>Instead of hoisting package installs in <code>node_modules</code>, install packages in
the same manner that they are depended on. This may cause very deep
directory structures and duplicate package installs as there is no
de-duplicating. Sets <code>--install-strategy=nested</code>.</p>
<h4 id="global-style"><code>global-style</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
<li>DEPRECATED: This option has been deprecated in favor of
<code>--install-strategy=shallow</code></li>
</ul>
<p>Only install direct dependencies in the top level <code>node_modules</code>, but hoist
on deeper dependencies. Sets <code>--install-strategy=shallow</code>.</p>
<h4 id="omit"><code>omit</code></h4>
<ul>
<li>Default: 'dev' if the <code>NODE_ENV</code> environment variable is set to
'production', otherwise empty.</li>
<li>Type: "dev", "optional", or "peer" (can be set multiple times)</li>
</ul>
<p>Dependency types to omit from the installation tree on disk.</p>
<p>Note that these dependencies <em>are</em> still resolved and added to the
<code>package-lock.json</code> or <code>npm-shrinkwrap.json</code> file. They are just not
physically installed on disk.</p>
<p>If a package type appears in both the <code>--include</code> and <code>--omit</code> lists, then
it will be included.</p>
<p>If the resulting omit list includes <code>'dev'</code>, then the <code>NODE_ENV</code> environment
variable will be set to <code>'production'</code> for all lifecycle scripts.</p>
<h4 id="include"><code>include</code></h4>
<ul>
<li>Default:</li>
<li>Type: "prod", "dev", "optional", or "peer" (can be set multiple times)</li>
</ul>
<p>Option that allows for defining which types of dependencies to install.</p>
<p>This is the inverse of <code>--omit=&lt;type&gt;</code>.</p>
<p>Dependency types specified in <code>--include</code> will not be omitted, regardless of
the order in which omit/include are specified on the command-line.</p>
<h4 id="strict-peer-deps"><code>strict-peer-deps</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to <code>true</code>, and <code>--legacy-peer-deps</code> is not set, then <em>any</em>
conflicting <code>peerDependencies</code> will be treated as an install failure, even
if npm could reasonably guess the appropriate resolution based on non-peer
dependency relationships.</p>
<p>By default, conflicting <code>peerDependencies</code> deep in the dependency graph will
be resolved using the nearest non-peer dependency specification, even if
doing so will result in some packages receiving a peer dependency outside
the range set in their package's <code>peerDependencies</code> object.</p>
<p>When such an override is performed, a warning is printed, explaining the
conflict and the packages involved. If <code>--strict-peer-deps</code> is set, then
this warning is treated as a failure.</p>
<h4 id="package-lock"><code>package-lock</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>If set to false, then ignore <code>package-lock.json</code> files when installing. This
will also prevent <em>writing</em> <code>package-lock.json</code> if <code>save</code> is true.</p>
<h4 id="foreground-scripts"><code>foreground-scripts</code></h4>
<ul>
<li>Default: <code>false</code> unless when using <code>npm pack</code> or <code>npm publish</code> where it
defaults to <code>true</code></li>
<li>Type: Boolean</li>
</ul>
<p>Run all build scripts (ie, <code>preinstall</code>, <code>install</code>, and <code>postinstall</code>)
scripts for installed packages in the foreground process, sharing standard
input, output, and error with the main npm process.</p>
<p>Note that this will generally make installs run slower, and be much noisier,
but can be useful for debugging.</p>
<h4 id="ignore-scripts"><code>ignore-scripts</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, npm does not run scripts specified in package.json files.</p>
<p>Note that commands explicitly intended to run a particular script, such as
<code>npm start</code>, <code>npm stop</code>, <code>npm restart</code>, <code>npm test</code>, and <code>npm run-script</code>
will still run their intended script if <code>ignore-scripts</code> is set, but they
will <em>not</em> run any pre- or post-scripts.</p>
<h4 id="audit"><code>audit</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>When "true" submit audit reports alongside the current npm command to the
default registry and all registries configured for scopes. See the
documentation for <a href="../commands/npm-audit.html"><code>npm audit</code></a> for details on what is
submitted.</p>
<h4 id="bin-links"><code>bin-links</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Tells npm to create symlinks (or <code>.cmd</code> shims on Windows) for package
executables.</p>
<p>Set to false to have it not do this. This can be used to work around the
fact that some file systems don't support symlinks, even on ostensibly Unix
systems.</p>
<h4 id="fund"><code>fund</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>When "true" displays the message at the end of each <code>npm install</code>
acknowledging the number of dependencies looking for funding. See <a href="../commands/npm-fund.html"><code>npm fund</code></a> for details.</p>
<h4 id="dry-run"><code>dry-run</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Indicates that you don't want npm to make any changes and that it should
only report what it would have done. This can be passed into any of the
commands that modify your local installation, eg, <code>install</code>, <code>update</code>,
<code>dedupe</code>, <code>uninstall</code>, as well as <code>pack</code> and <code>publish</code>.</p>
<p>Note: This is NOT honored by other network related commands, eg <code>dist-tags</code>,
<code>owner</code>, etc.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="install-links"><code>install-links</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>When set file: protocol dependencies will be packed and installed as regular
dependencies instead of creating a symlink. This option has no effect on
workspaces.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-outdated.html">npm outdated</a></li>
<li><a href="../commands/npm-shrinkwrap.html">npm shrinkwrap</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../configuring-npm/folders.html">npm folders</a></li>
<li><a href="../commands/npm-ls.html">npm ls</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-update.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>