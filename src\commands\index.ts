import { Command, Flags, Args } from '@oclif/core';

export default class AlgoTrade extends Command {
  static override description = 'AlgoTrade Framework CLI - A simple trading framework command line interface';

  static override examples = [
    '<%= config.bin %> <%= command.id %>',
    '<%= config.bin %> <%= command.id %> --name=myname',
    '<%= config.bin %> <%= command.id %> --version',
  ];

  static override flags = {
    // add --version flag to show CLI version
    version: Flags.version({ char: 'v' }),
    // add --help flag to show CLI version
    help: Flags.help({ char: 'h' }),
    // flag with a value (-n, --name=VALUE)
    name: Flags.string({ char: 'n', description: 'name to print' }),
    // flag with no value (-f, --force)
    force: Flags.boolean({ char: 'f' }),
  };

  static override args = {
    file: Args.string({ description: 'file to read', required: false }),
  };

  async run(): Promise<void> {
    const { args, flags } = await this.parse(AlgoTrade);

    const name = flags.name ?? 'world';
    this.log(`🚀 Hello ${name} from AlgoTrade Framework!`);

    if (args.file && flags.force) {
      this.log(`You input --force and --file: ${args.file}`);
    }

    // Display some sample trading-related information
    this.log('\n📊 AlgoTrade Framework Features:');
    this.log('  • Portfolio management');
    this.log('  • Strategy backtesting');
    this.log('  • Risk analysis');
    this.log('  • Market data integration');

    this.log('\n💡 Use --help to see all available commands');
  }
}
