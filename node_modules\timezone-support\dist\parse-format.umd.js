!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r(n["timezone-parse-format"]={})}(this,function(n){"use strict";var i=/(\[[^[]*\])|([-:/.()\s]+)|(A|a|YYYY|YY?|MM?|DD?|d|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,r=/\d/,t=/\d\d/,e=/\d\d?/,o=/[+-]\d\d:?\d\d/,a={},f={},s={};function u(n,r){a[n]=r}function h(n,r){"string"==typeof n&&(n=[n]);var t="string"==typeof r?function(n){this[r]=+n}:r,e=n,o=Array.isArray(e),i=0;for(e=o?e:e[Symbol.iterator]();;){var a;if(o){if(i>=e.length)break;a=e[i++]}else{if((i=e.next()).done)break;a=i.value}f[a]=t}}u("A",/[AP]M/),h(["A"],function(n){this.afternoon="PM"===n}),u("a",/[ap]m/),h(["a"],function(n){this.afternoon="pm"===n}),u("S",r),u("SS",t),u("SSS",/\d{3}/);for(var c=function(n,r){h(n,function(n){this.milliseconds=+n*r})},d="S",l=100;1<=l;d+="S",l/=10)c(d,l);u("s",e),u("ss",t),h(["s","ss"],"seconds"),u("m",e),u("mm",t),h(["m","mm"],"minutes"),u("H",e),u("h",e),u("HH",t),u("hh",t),h(["H","HH","h","hh"],"hours"),u("d",r),h("d","dayOfWeek"),u("D",e),u("DD",t),h(["D","DD"],"day"),u("M",e),u("MM",t),h(["M","MM"],"month"),u("Y",/[+-]?\d+/),u("YY",t),u("YYYY",/\d{4}/),h(["Y","YYYY"],"year"),h("YY",function(n){n=+n,this.year=n+(68<n?1900:2e3)}),u("z",/[A-Z]{3,4}/),h("z",function(n){(this.zone||(this.zone={})).abbreviation=n}),u("Z",o),u("ZZ",o),h(["Z","ZZ"],function(n){var r,t;(this.zone||(this.zone={})).offset=(r=n.match(/([+-]|\d\d)/g),0===(t=60*r[1]+ +r[2])?0:"+"===r[0]?-t:t)});var m=[void 0,void 0,function(n){return 9<n?n:"0"+n},function(n){return 99<n?n:9<n?"0"+n:"00"+n},function(n){return 999<n?n:99<n?"0"+n:9<n?"00"+n:"000"+n}];function v(n,r){return m[r](n)}var Y={},y={};var p=function(n,r,t){var e="string"==typeof t?function(){return this[t]}:t;n&&(Y[n]=e),r&&(Y[r[0]]=function(){return v(e.call(this),r[1])})};function M(n,t){p(n,0,function(){var n=-this.zone.offset,r=n<0?"-":"+";return n=Math.abs(n),r+v(Math.floor(n/60),2)+t+v(n%60,2)})}p("A",0,function(){return this.hours<12?"AM":"PM"}),p("a",0,function(){return this.hours<12?"am":"pm"}),p("S",0,function(){return Math.floor(this.milliseconds/100)}),p(0,["SS",2],function(){return Math.floor(this.milliseconds/10)}),p(0,["SSS",3],"milliseconds"),p("s",["ss",2],"seconds"),p("m",["mm",2],"minutes"),p("h",["hh",2],function(){return this.hours%12||12}),p("H",["HH",2],"hours"),p("d",0,"dayOfWeek"),p("D",["DD",2],"day"),p("M",["MM",2],"month"),p(0,["YY",2],function(){return this.year%100}),p("Y",["YYYY",4],"year"),p("z",0,function(){return this.zone.abbreviation}),M("Z",":"),M("ZZ",""),n.parseZonedTime=function(n,r){var t=s[r];return t||(t=s[r]=function(n){var c=n.match(i);if(!c)throw new Error('Invalid format: "'+n+'".');for(var d=c.length,r=0;r<d;++r){var t=c[r],e=a[t],o=f[t];c[r]=o?{regex:e,parser:o}:t.replace(/^\[|\]$/g,"")}return function(n){for(var r={},t=0,e=0;t<d;++t){var o=c[t];if("string"==typeof o){if(n.indexOf(o,e)!==e){var i=n.substr(e,o.length);throw new Error('Expected "'+o+'" at character '+e+', found "'+i+'".')}e+=o.length}else{var a=o.regex,f=o.parser,s=n.substr(e),u=a.exec(s);if(!u||0!==u.index)throw new Error('Matching "'+a+'" at character '+e+' failed with "'+s+'".');var h=u[0];f.call(r,h),e+=h.length}}return function(n){var r=n.afternoon;if(void 0!==r){var t=n.hours;r?t<12&&(n.hours+=12):12===t&&(n.hours=0),delete n.afternoon}}(r),r}}(r)),t(n)},n.formatZonedTime=function(n,r){var t=y[r];return t||(t=y[r]=function(n){for(var f=n.match(i),r=f.length,t=0;t<r;++t){var e=f[t],o=Y[e];f[t]=o||e.replace(/^\[|\]$/g,"")}return function(n){var r="",t=f,e=Array.isArray(t),o=0;for(t=e?t:t[Symbol.iterator]();;){var i;if(e){if(o>=t.length)break;i=t[o++]}else{if((o=t.next()).done)break;i=o.value}var a=i;r+="function"==typeof a?a.call(n):a}return r}}(r)),t(n)},Object.defineProperty(n,"__esModule",{value:!0})});
//# sourceMappingURL=parse-format.umd.js.map
