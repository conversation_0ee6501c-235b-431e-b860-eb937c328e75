<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-token</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-token----1093">
    <span>npm-token</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">Manage your authentication tokens</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#read-only"><code>read-only</code></a></li><li><a href="#cidr"><code>cidr</code></a></li><li><a href="#registry"><code>registry</code></a></li><li><a href="#otp"><code>otp</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm token list
npm token revoke &lt;id|token&gt;
npm token create [--read-only] [--cidr=list]
</code></pre>
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>This lets you list, create and revoke authentication tokens.</p>
<ul>
<li><code>npm token list</code>:
Shows a table of all active authentication tokens. You can request
this as JSON with <code>--json</code> or tab-separated values with <code>--parseable</code>.</li>
</ul>
<pre><code>Read only token npm_1f… with id 7f3134 created 2017-10-21

Publish token npm_af…  with id c03241 created 2017-10-02
with IP Whitelist: ***********/24

Publish token npm_… with id e0cf92 created 2017-10-02

</code></pre>
<ul>
<li>
<p><code>npm token create [--read-only] [--cidr=&lt;cidr-ranges&gt;]</code>:
Create a new authentication token. It can be <code>--read-only</code>, or accept
a list of
<a href="https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing">CIDR</a>
ranges with which to limit use of this token. This will prompt you for
your password, and, if you have two-factor authentication enabled, an
otp.</p>
<p>Currently, the cli can not generate automation tokens. Please refer to
the <a href="https://docs.npmjs.com/creating-and-viewing-access-tokens">docs
website</a>
for more information on generating automation tokens.</p>
</li>
</ul>
<pre><code>Created publish token a73c9572-f1b9-8983-983d-ba3ac3cc913d
</code></pre>
<ul>
<li><code>npm token revoke &lt;token|id&gt;</code>:
Immediately removes an authentication token from the registry.  You
will no longer be able to use it.  This can accept both complete
tokens (such as those you get back from <code>npm token create</code>, and those
found in your <code>.npmrc</code>), and ids as seen in the parseable or json
output of <code>npm token list</code>.  This will NOT accept the truncated token
found in the normal <code>npm token list</code> output.</li>
</ul>
<h3 id="configuration">Configuration</h3>
<h4 id="read-only"><code>read-only</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>This is used to mark a token as unable to publish when configuring limited
access tokens with the <code>npm token create</code> command.</p>
<h4 id="cidr"><code>cidr</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or String (can be set multiple times)</li>
</ul>
<p>This is a list of CIDR address to be used when configuring limited access
tokens with the <code>npm token create</code> command.</p>
<h4 id="registry"><code>registry</code></h4>
<ul>
<li>Default: "<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>"</li>
<li>Type: URL</li>
</ul>
<p>The base URL of the npm registry.</p>
<h4 id="otp"><code>otp</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or String</li>
</ul>
<p>This is a one-time password from a two-factor authenticator. It's needed
when publishing or changing package permissions with <code>npm access</code>.</p>
<p>If not set, and a registry response fails with a challenge for a one-time
password, npm will prompt on the command line for one.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-adduser.html">npm adduser</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../commands/npm-owner.html">npm owner</a></li>
<li><a href="../commands/npm-whoami.html">npm whoami</a></li>
<li><a href="../commands/npm-profile.html">npm profile</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-token.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>