<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-ls</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-ls----1093">
    <span>npm-ls</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">List installed packages</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#note-design-changes-pending">Note: Design Changes Pending</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#all"><code>all</code></a></li><li><a href="#json"><code>json</code></a></li><li><a href="#long"><code>long</code></a></li><li><a href="#parseable"><code>parseable</code></a></li><li><a href="#global"><code>global</code></a></li><li><a href="#depth"><code>depth</code></a></li><li><a href="#omit"><code>omit</code></a></li><li><a href="#include"><code>include</code></a></li><li><a href="#link"><code>link</code></a></li><li><a href="#package-lock-only"><code>package-lock-only</code></a></li><li><a href="#unicode"><code>unicode</code></a></li><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li><li><a href="#install-links"><code>install-links</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm ls &lt;package-spec&gt;

alias: list
</code></pre>
<h3 id="description">Description</h3>
<p>This command will print to stdout all the versions of packages that are
installed, as well as their dependencies when <code>--all</code> is specified, in a
tree structure.</p>
<p>Note: to get a "bottoms up" view of why a given package is included in the
tree at all, use <a href="../commands/npm-explain.html"><code>npm explain</code></a>.</p>
<p>Positional arguments are <code>name@version-range</code> identifiers, which will limit
the results to only the paths to the packages named.  Note that nested
packages will <em>also</em> show the paths to the specified packages.  For
example, running <code>npm ls promzard</code> in npm's source tree will show:</p>
<pre><code class="language-bash">npm@10.9.3 /path/to/npm
└─┬ init-package-json@0.0.4
  └── promzard@0.1.5
</code></pre>
<p>It will print out extraneous, missing, and invalid packages.</p>
<p>If a project specifies git urls for dependencies these are shown
in parentheses after the <code>name@version</code> to make it easier for users to
recognize potential forks of a project.</p>
<p>The tree shown is the logical dependency tree, based on package
dependencies, not the physical layout of your <code>node_modules</code> folder.</p>
<p>When run as <code>ll</code> or <code>la</code>, it shows extended information by default.</p>
<h3 id="note-design-changes-pending">Note: Design Changes Pending</h3>
<p>The <code>npm ls</code> command's output and behavior made a <em>ton</em> of sense when npm
created a <code>node_modules</code> folder that naively nested every dependency.  In
such a case, the logical dependency graph and physical tree of packages on
disk would be roughly identical.</p>
<p>With the advent of automatic install-time deduplication of dependencies in
npm v3, the <code>ls</code> output was modified to display the logical dependency
graph as a tree structure, since this was more useful to most users.
However, without using <code>npm ls -l</code>, it became impossible to show <em>where</em> a
package was actually installed much of the time!</p>
<p>With the advent of automatic installation of <code>peerDependencies</code> in npm v7,
this gets even more curious, as <code>peerDependencies</code> are logically
"underneath" their dependents in the dependency graph, but are always
physically at or above their location on disk.</p>
<p>Also, in the years since npm got an <code>ls</code> command (in version 0.0.2!),
dependency graphs have gotten much larger as a general rule.  Therefore, in
order to avoid dumping an excessive amount of content to the terminal, <code>npm ls</code> now only shows the <em>top</em> level dependencies, unless <code>--all</code> is
provided.</p>
<p>A thorough re-examination of the use cases, intention, behavior, and output
of this command, is currently underway.  Expect significant changes to at
least the default human-readable <code>npm ls</code> output in npm v8.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="all"><code>all</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>When running <code>npm outdated</code> and <code>npm ls</code>, setting <code>--all</code> will show all
outdated or installed packages, rather than only those directly depended
upon by the current project.</p>
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<h4 id="long"><code>long</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information in <code>ls</code>, <code>search</code>, and <code>help-search</code>.</p>
<h4 id="parseable"><code>parseable</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Output parseable results from commands that write to standard output. For
<code>npm search</code>, this will be tab-separated table format.</p>
<h4 id="global"><code>global</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Operates in "global" mode, so that packages are installed into the <code>prefix</code>
folder instead of the current working directory. See
<a href="../configuring-npm/folders.html">folders</a> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead
of the current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<h4 id="depth"><code>depth</code></h4>
<ul>
<li>Default: <code>Infinity</code> if <code>--all</code> is set, otherwise <code>1</code></li>
<li>Type: null or Number</li>
</ul>
<p>The depth to go when recursing packages for <code>npm ls</code>.</p>
<p>If not set, <code>npm ls</code> will show only the immediate dependencies of the root
project. If <code>--all</code> is set, then npm will show all dependencies by default.</p>
<h4 id="omit"><code>omit</code></h4>
<ul>
<li>Default: 'dev' if the <code>NODE_ENV</code> environment variable is set to
'production', otherwise empty.</li>
<li>Type: "dev", "optional", or "peer" (can be set multiple times)</li>
</ul>
<p>Dependency types to omit from the installation tree on disk.</p>
<p>Note that these dependencies <em>are</em> still resolved and added to the
<code>package-lock.json</code> or <code>npm-shrinkwrap.json</code> file. They are just not
physically installed on disk.</p>
<p>If a package type appears in both the <code>--include</code> and <code>--omit</code> lists, then
it will be included.</p>
<p>If the resulting omit list includes <code>'dev'</code>, then the <code>NODE_ENV</code> environment
variable will be set to <code>'production'</code> for all lifecycle scripts.</p>
<h4 id="include"><code>include</code></h4>
<ul>
<li>Default:</li>
<li>Type: "prod", "dev", "optional", or "peer" (can be set multiple times)</li>
</ul>
<p>Option that allows for defining which types of dependencies to install.</p>
<p>This is the inverse of <code>--omit=&lt;type&gt;</code>.</p>
<p>Dependency types specified in <code>--include</code> will not be omitted, regardless of
the order in which omit/include are specified on the command-line.</p>
<h4 id="link"><code>link</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Used with <code>npm ls</code>, limiting output to only those packages that are linked.</p>
<h4 id="package-lock-only"><code>package-lock-only</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, the current operation will only use the <code>package-lock.json</code>,
ignoring <code>node_modules</code>.</p>
<p>For <code>update</code> this means only the <code>package-lock.json</code> will be updated,
instead of checking <code>node_modules</code> and downloading dependencies.</p>
<p>For <code>list</code> this means the output will be based on the tree described by the
<code>package-lock.json</code>, rather than the contents of <code>node_modules</code>.</p>
<h4 id="unicode"><code>unicode</code></h4>
<ul>
<li>Default: false on windows, true on mac/unix systems with a unicode locale,
as defined by the <code>LC_ALL</code>, <code>LC_CTYPE</code>, or <code>LANG</code> environment variables.</li>
<li>Type: Boolean</li>
</ul>
<p>When set to true, npm uses unicode characters in the tree output. When
false, it uses ascii characters instead of unicode glyphs.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="install-links"><code>install-links</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>When set file: protocol dependencies will be packed and installed as regular
dependencies instead of creating a symlink. This option has no effect on
workspaces.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../using-npm/package-spec.html">package spec</a></li>
<li><a href="../commands/npm-explain.html">npm explain</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../configuring-npm/folders.html">npm folders</a></li>
<li><a href="../commands/npm-explain.html">npm explain</a></li>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-link.html">npm link</a></li>
<li><a href="../commands/npm-prune.html">npm prune</a></li>
<li><a href="../commands/npm-outdated.html">npm outdated</a></li>
<li><a href="../commands/npm-update.html">npm update</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-ls.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>