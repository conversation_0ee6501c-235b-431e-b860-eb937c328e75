import { Interval } from "./interface";
import { I<PERSON>roker } from "./interface/IBroker";
import { Quote, OHLC, Instrument, MarketTiming } from "./models";
import EventEmitter from "events";

// ---------------- Types ----------------
export interface BlotterOptions {
  intervals: Interval[];
  instruments: Instrument[];  // Stronger typing than string[]
  preloadDays?: number;
}

interface BarState {
  ticks: Quote[];
  timer?: NodeJS.Timeout;
}

interface BlotterEvents {
  tick: (tick: Quote) => void;
  bar: (symbol: string, interval: Interval, bar: OHLC) => void;
}

// ---------------- Typed EventEmitter ----------------
class TypedEventEmitter<Events> extends EventEmitter {
  public on<K extends keyof Events>(event: K, listener: Events[K]): this;
  public on(eventName: string | symbol, listener: (...args: any[]) => void): this;
  public on(event: any, listener:
// ---------------- Blotter ----------------
export class Blotter extends TypedEventEmitter<BlotterEvents> {
  private broker: IBroker;
  private intervals: Interval[];
  private instruments: Instrument[];
  private preloadDays: number;

  private tickBuffer: Map<string, BarState> = new Map();
  private bars: Map<string, Map<Interval, OHLC[]>> = new Map();

  constructor(broker: IBroker, options: BlotterOptions) {
    super();
    this.broker = broker;
    this.intervals = options.intervals;
    this.instruments = options.instruments;
    this.preloadDays = options.preloadDays || 0;

    for (const inst of this.instruments) {
      this.tickBuffer.set(inst.symbol, { ticks: [] });
      const map = new Map<Interval, OHLC[]>();
      this.intervals.forEach((iv) => map.set(iv, []));
      this.bars.set(inst.symbol, map);
    }
  }

  /** Start blotter: connect ticker and schedule bars */
  public async start() {
    const marketTiming = this.broker.getTiming();

    if (this.preloadDays > 0) {
      await this.preloadHistoricalBars(marketTiming);
    }

    await this.broker.connectTicker({
      onTicks: (ticks) => this.onTicks(ticks),
    });

    const instrumentTokens = this.instruments.map((i) => i.instrumentToken as number);
    this.broker.subscribeTicks(instrumentTokens);

    for (const inst of this.instruments) {
      for (const interval of this.intervals) {
        this.scheduleNextBar(inst.symbol, interval, marketTiming);
      }
    }
  }

  private async preloadHistoricalBars(marketTiming: MarketTiming) {
    const from = new Date();
    from.setDate(from.getDate() - this.preloadDays);

    for (const inst of this.instruments) {
      for (const interval of this.intervals) {
        const historical = await this.broker.getHistoricalData({
          instrumentToken: inst.instrumentToken,
          interval,
          from,
          to: new Date(),
        });
        this.bars.get(inst.symbol)?.get(interval)?.push(...historical);
      }
    }
  }

  private onTicks(ticks: Quote[]) {
    for (const tick of ticks) {
      const state = this.tickBuffer.get(tick.symbol);
      if (!state) continue;
      state.ticks.push(tick);
      this.emit("tick", tick);
    }
  }

  private scheduleNextBar(symbol: string, interval: Interval, marketTiming: MarketTiming) {
    const now = this.getCurrentTime(marketTiming.timezone);
    const { barStart, barEnd } = this.getAlignedBarTime(now, marketTiming.marketStartTime, interval);

    const delay = barEnd.getTime() - now.getTime();

    const state = this.tickBuffer.get(symbol);
    if (!state) return;

    state.timer = setTimeout(() => {
      this.emitBar(symbol, interval);
      this.scheduleNextBar(symbol, interval, marketTiming);
    }, delay);
  }

  private emitBar(symbol: string, interval: Interval) {
    const state = this.tickBuffer.get(symbol);
    if (!state || state.ticks.length === 0) return;

    const ticks = state.ticks;
    const ohlc: OHLC = {
      symbol,
      open: ticks[0]!.lastPrice,
      high: Math.max(...ticks.map((t) => t.lastPrice)),
      low: Math.min(...ticks.map((t) => t.lastPrice)),
      close: ticks[ticks.length - 1]!.lastPrice,
      timestamp: new Date(),
    };

    this.bars.get(symbol)?.get(interval)?.push(ohlc);
    this.emit("bar", symbol, interval, ohlc);

    state.ticks = [];
  }

  private getAlignedBarTime(now: Date, marketStart: Date, interval: Interval) {
    const intervalMinutes = this.convertIntervalToMinutes(interval);
    const minutesSinceOpen = Math.floor((now.getTime() - marketStart.getTime()) / 60000);
    const completedIntervals = Math.floor(minutesSinceOpen / intervalMinutes);

    const barStart = new Date(marketStart.getTime() + completedIntervals * intervalMinutes * 60000);
    const barEnd = new Date(barStart.getTime() + intervalMinutes * 60000);

    return { barStart, barEnd };
  }

  private convertIntervalToMinutes(interval: Interval): number {
    const mapping: Record<Interval, number> = {
      "1m": 1, "5m": 5, "10m": 10,
      "15m": 15, "30m": 30, "60m": 60,
      "1d": 24 * 60, "1w": 7 * 24 * 60,
    };
    return mapping[interval];
  }

  private getCurrentTime(timezone: string): Date {
    return new Date(new Date().toLocaleString("en-US", { timeZone: timezone }));
  }

  public stop() {
    for (const state of this.tickBuffer.values()) {
      if (state.timer) clearTimeout(state.timer);
    }
    this.broker.disconnectTicker();
  }

  public getBars(symbol: string, interval: Interval): OHLC[] {
    return this.bars.get(symbol)?.get(interval) || [];
  }
}
