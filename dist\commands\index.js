"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@oclif/core");
class AlgoTrade extends core_1.Command {
    async run() {
        const { args, flags } = await this.parse(AlgoTrade);
        const name = flags.name ?? 'world';
        this.log(`🚀 Hello ${name} from AlgoTrade Framework!`);
        if (args.file && flags.force) {
            this.log(`You input --force and --file: ${args.file}`);
        }
        this.log('\n📊 AlgoTrade Framework Features:');
        this.log('  • Portfolio management');
        this.log('  • Strategy backtesting');
        this.log('  • Risk analysis');
        this.log('  • Market data integration');
        this.log('\n💡 Use --help to see all available commands');
    }
}
AlgoTrade.description = 'AlgoTrade Framework CLI - A simple trading framework command line interface';
AlgoTrade.examples = [
    '<%= config.bin %> <%= command.id %>',
    '<%= config.bin %> <%= command.id %> --name=myname',
    '<%= config.bin %> <%= command.id %> --version',
];
AlgoTrade.flags = {
    version: core_1.Flags.version({ char: 'v' }),
    help: core_1.Flags.help({ char: 'h' }),
    name: core_1.Flags.string({ char: 'n', description: 'name to print' }),
    force: core_1.Flags.boolean({ char: 'f' }),
};
AlgoTrade.args = {
    file: core_1.Args.string({ description: 'file to read', required: false }),
};
exports.default = AlgoTrade;
//# sourceMappingURL=index.js.map