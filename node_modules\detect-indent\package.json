{"name": "detect-indent", "version": "7.0.1", "description": "Detect the indentation of code", "license": "MIT", "repository": "sindresorhus/detect-indent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["indent", "indentation", "detect", "infer", "identify", "code", "string", "text", "source", "space", "tab"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "typescript": "^4.3.5", "xo": "^0.44.0"}, "xo": {"ignores": ["fixture"]}}