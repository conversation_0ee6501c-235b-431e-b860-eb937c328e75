.TH "NPM-STAR" "1" "June 2025" "NPM@10.9.3" ""
.SH "NAME"
\fBnpm-star\fR - Mark your favorite packages
.SS "Synopsis"
.P
.RS 2
.nf
npm star \[lB]<package-spec>...\[rB]
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
"Starring" a package means that you have some interest in it. It's a vaguely positive way to show that you care.
.P
It's a boolean thing. Starring repeatedly has no additional effect.
.SS "More"
.P
There's also these extra commands to help you manage your favorite packages:
.SS "Unstar"
.P
You can also "unstar" a package using npm help unstar
.P
"Unstarring" is the same thing, but in reverse.
.SS "Listing stars"
.P
You can see all your starred packages using npm help stars
.SS "Configuration"
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "\fBunicode\fR"
.RS 0
.IP \(bu 4
Default: false on windows, true on mac/unix systems with a unicode locale, as defined by the \fBLC_ALL\fR, \fBLC_CTYPE\fR, or \fBLANG\fR environment variables.
.IP \(bu 4
Type: Boolean
.RE 0

.P
When set to true, npm uses unicode characters in the tree output. When false, it uses ascii characters instead of unicode glyphs.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help "package spec"
.IP \(bu 4
npm help unstar
.IP \(bu 4
npm help stars
.IP \(bu 4
npm help view
.IP \(bu 4
npm help whoami
.IP \(bu 4
npm help adduser
.RE 0
