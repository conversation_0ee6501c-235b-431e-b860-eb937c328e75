import { Command } from '@oclif/core';
export default class Market extends Command {
    static description: string;
    static examples: string[];
    static flags: {
        help: import("@oclif/core/lib/interfaces").BooleanFlag<void>;
        quote: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
        watchlist: import("@oclif/core/lib/interfaces").BooleanFlag<boolean>;
        trending: import("@oclif/core/lib/interfaces").BooleanFlag<boolean>;
        add: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
        remove: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
    };
    private sampleData;
    run(): Promise<void>;
}
//# sourceMappingURL=market.d.ts.map