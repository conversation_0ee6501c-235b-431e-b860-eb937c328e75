import { Command } from '@oclif/core';
export default class AlgoTrade extends Command {
    static description: string;
    static examples: string[];
    static flags: {
        version: import("@oclif/core/lib/interfaces").BooleanFlag<void>;
        help: import("@oclif/core/lib/interfaces").BooleanFlag<void>;
        name: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
        force: import("@oclif/core/lib/interfaces").BooleanFlag<boolean>;
    };
    static args: {
        file: import("@oclif/core/lib/interfaces").Arg<string | undefined, Record<string, unknown>>;
    };
    run(): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map