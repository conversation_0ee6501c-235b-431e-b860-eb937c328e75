#!/usr/bin/env node

'use strict'

const commander = require('commander')
const { createTimeZoneData } = require('../util/data-creator')
const pkg = require('../package.json')

commander.version(pkg.version)
  .description('Generates time zone data for a selected year range.')
  .usage('[options] <first year> <last year>')
  .option('-a, --all-years', 'includes all available years')
  .option('-c, --as-cjs-module', 'format the time zone data as a CommonJS module')
  .option('-d, --as-amd-module', 'format the time zone data as an AMD module')
  .option('-m, --as-module', 'format the time zone data as a JavaScript module')
  .option('-n, --umd-name <name>', 'UMD global export name, if not "timeZoneData"')
  .option('-o, --output-file <file>', 'write the time zone data to a file')
  .option('-u, --as-umd-module', 'format the time zone data as an UMD module')
  .option('-o, --output-file <file>', 'write the time zone data to a file')

commander.on('--help', function () {
  console.log()
  console.log('  Time zone data are printed on the standard output as JSON by default.')
  console.log()
  console.log('  Examples:')
  console.log()
  console.log('    $ create-timezone-data 2012 2022')
  console.log('    $ create-timezone-data -m -o custom-data.js 1970 2038')
})

commander.parse(process.argv)
const [ firstYear, lastYear ] = commander.args
if (!(firstYear && lastYear) && !commander.allYears) {
  commander.help()
}

const {
  asModule, asCjsModule, asAmdModule, asUmdModule, umdName, outputFile
} = commander
createTimeZoneData({
  firstYear,
  lastYear,
  asModule,
  asCjsModule,
  asAmdModule,
  asUmdModule,
  umdName,
  outputFile
})
  .then(timeZoneData => {
    if (!outputFile) {
      console.log(timeZoneData)
    }
  })
  .catch(error => {
    console.error(error.message)
    process.exitCode = 1
  })
