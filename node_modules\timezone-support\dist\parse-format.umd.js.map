{"version": 3, "file": "parse-format.umd.js", "sources": ["../src/parse-format/tokens.js", "../src/parse-format/parse.js", "../src/parse-format/padding.js", "../src/parse-format/format.js"], "sourcesContent": ["export default /(\\[[^[]*\\])|([-:/.()\\s]+)|(A|a|YYYY|YY?|MM?|DD?|d|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g\n", "import formattingTokens from './tokens'\n\nconst match1 = /\\d/ // 0 - 9\nconst match2 = /\\d\\d/ // 00 - 99\nconst match3 = /\\d{3}/ // 000 - 999\nconst match4 = /\\d{4}/ // 0000 - 9999\nconst match1to2 = /\\d\\d?/ // 0 - 99\nconst matchUpperAMPM = /[AP]M/\nconst matchLowerAMPM = /[ap]m/\nconst matchSigned = /[+-]?\\d+/ // -inf - inf\nconst matchOffset = /[+-]\\d\\d:?\\d\\d/ // +00:00 -00:00 +0000 or -0000\nconst matchAbbreviation = /[A-Z]{3,4}/ // CET\n\nconst parseTokenExpressions = {}\nconst parseTokenFunctions = {}\nconst parsers = {}\n\nfunction correctDayPeriod (time) {\n  const { afternoon } = time\n  if (afternoon !== undefined) {\n    const { hours } = time\n    if (afternoon) {\n      if (hours < 12) {\n        time.hours += 12\n      }\n    } else {\n      if (hours === 12) {\n        time.hours = 0\n      }\n    }\n    delete time.afternoon\n  }\n}\n\nfunction makeParser (format) {\n  const array = format.match(formattingTokens)\n  if (!array) {\n    throw new Error(`Invalid format: \"${format}\".`)\n  }\n  const { length } = array\n  for (let i = 0; i < length; ++i) {\n    const token = array[i]\n    const regex = parseTokenExpressions[token]\n    const parser = parseTokenFunctions[token]\n    if (parser) {\n      array[i] = { regex, parser }\n    } else {\n      array[i] = token.replace(/^\\[|\\]$/g, '')\n    }\n  }\n  return function (input) {\n    const time = {}\n    for (let i = 0, start = 0; i < length; ++i) {\n      const token = array[i]\n      if (typeof token === 'string') {\n        if (input.indexOf(token, start) !== start) {\n          const part = input.substr(start, token.length)\n          throw new Error(`Expected \"${token}\" at character ${start}, found \"${part}\".`)\n        }\n        start += token.length\n      } else {\n        const { regex, parser } = token\n        const part = input.substr(start)\n        const match = regex.exec(part)\n        if (!match || match.index !== 0) {\n          throw new Error(`Matching \"${regex}\" at character ${start} failed with \"${part}\".`)\n        }\n        const value = match[0]\n        parser.call(time, value)\n        start += value.length\n      }\n    }\n    correctDayPeriod(time)\n    return time\n  }\n}\n\nfunction addExpressionToken (token, regex) {\n  parseTokenExpressions[token] = regex\n}\n\nfunction addParseToken (tokens, property) {\n  if (typeof tokens === 'string') {\n    tokens = [ tokens ]\n  }\n  const callback = typeof property === 'string' ? function (input) {\n    this[property] = +input\n  } : property\n  for (let token of tokens) {\n    parseTokenFunctions[token] = callback\n  }\n}\n\nfunction offsetFromString (string) {\n  const parts = string.match(/([+-]|\\d\\d)/g)\n  const minutes = +(parts[1] * 60) + +parts[2]\n  return minutes === 0 ? 0 : parts[0] === '+' ? -minutes : minutes\n}\n\naddExpressionToken('A', matchUpperAMPM)\naddParseToken(['A'], function (input) {\n  this.afternoon = input === 'PM'\n})\n\naddExpressionToken('a', matchLowerAMPM)\naddParseToken(['a'], function (input) {\n  this.afternoon = input === 'pm'\n})\n\naddExpressionToken('S', match1)\naddExpressionToken('SS', match2)\naddExpressionToken('SSS', match3)\nfor (let token = 'S', factor = 100; factor >= 1; token += 'S', factor /= 10) {\n  addParseToken(token, function (input) {\n    this.milliseconds = +input * factor\n  })\n}\n\naddExpressionToken('s', match1to2)\naddExpressionToken('ss', match2)\naddParseToken(['s', 'ss'], 'seconds')\n\naddExpressionToken('m', match1to2)\naddExpressionToken('mm', match2)\naddParseToken(['m', 'mm'], 'minutes')\n\naddExpressionToken('H', match1to2)\naddExpressionToken('h', match1to2)\naddExpressionToken('HH', match2)\naddExpressionToken('hh', match2)\naddParseToken(['H', 'HH', 'h', 'hh'], 'hours')\n\naddExpressionToken('d', match1)\naddParseToken('d', 'dayOfWeek')\n\naddExpressionToken('D', match1to2)\naddExpressionToken('DD', match2)\naddParseToken(['D', 'DD'], 'day')\n\naddExpressionToken('M', match1to2)\naddExpressionToken('MM', match2)\naddParseToken(['M', 'MM'], 'month')\n\naddExpressionToken('Y', matchSigned)\naddExpressionToken('YY', match2)\naddExpressionToken('YYYY', match4)\naddParseToken(['Y', 'YYYY'], 'year')\naddParseToken('YY', function (input) {\n  input = +input\n  this.year = input + (input > 68 ? 1900 : 2000)\n})\n\naddExpressionToken('z', matchAbbreviation)\naddParseToken('z', function (input) {\n  const zone = this.zone || (this.zone = {})\n  zone.abbreviation = input\n})\n\naddExpressionToken('Z', matchOffset)\naddExpressionToken('ZZ', matchOffset)\naddParseToken(['Z', 'ZZ'], function (input) {\n  const zone = this.zone || (this.zone = {})\n  zone.offset = offsetFromString(input)\n})\n\nfunction parseZonedTime (input, format) {\n  let parser = parsers[format]\n  if (!parser) {\n    parser = parsers[format] = makeParser(format)\n  }\n  return parser(input)\n}\n\nexport { parseZonedTime }\n", "function padToTwo (number) {\n  return number > 9 ? number : '0' + number\n}\n\nfunction padToThree (number) {\n  return number > 99 ? number : number > 9 ? '0' + number : '00' + number\n}\n\nfunction padToFour (number) {\n  return number > 999 ? number : number > 99 ? '0' + number : number > 9 ? '00' + number : '000' + number\n}\n\nconst padToN = [ undefined, undefined, padToTwo, padToThree, padToFour ]\n\nfunction padWithZeros (number, length) {\n  return padToN[length](number)\n}\n\nexport { padWithZeros }\n", "import formattingTokens from './tokens'\nimport { padWithZ<PERSON>s } from './padding'\n\nconst formatTokenFunctions = {}\nconst formatters = {}\n\nfunction makeFormatter (format) {\n  const array = format.match(formattingTokens)\n  const { length } = array\n  for (let i = 0; i < length; ++i) {\n    const token = array[i]\n    const formatter = formatTokenFunctions[token]\n    if (formatter) {\n      array[i] = formatter\n    } else {\n      array[i] = token.replace(/^\\[|\\]$/g, '')\n    }\n  }\n  return function (time) {\n    let output = ''\n    for (let token of array) {\n      output += typeof token === 'function' ? token.call(time) : token\n    }\n    return output\n  }\n}\n\nconst addFormatToken = function (token, padded, property) {\n  const callback = typeof property === 'string' ? function () {\n    return this[property]\n  } : property\n  if (token) {\n    formatTokenFunctions[token] = callback\n  }\n  if (padded) {\n    formatTokenFunctions[padded[0]] = function () {\n      return padWithZeros(callback.call(this), padded[1])\n    }\n  }\n}\n\naddFormatToken('A', 0, function () { return this.hours < 12 ? 'AM' : 'PM' })\naddFormatToken('a', 0, function () { return this.hours < 12 ? 'am' : 'pm' })\naddFormatToken('S', 0, function () { return Math.floor(this.milliseconds / 100) })\naddFormatToken(0, ['SS', 2], function () { return Math.floor(this.milliseconds / 10) })\naddFormatToken(0, ['SSS', 3], 'milliseconds')\naddFormatToken('s', ['ss', 2], 'seconds')\naddFormatToken('m', ['mm', 2], 'minutes')\naddFormatToken('h', ['hh', 2], function () { return (this.hours % 12) || 12 })\naddFormatToken('H', ['HH', 2], 'hours')\naddFormatToken('d', 0, 'dayOfWeek')\naddFormatToken('D', ['DD', 2], 'day')\naddFormatToken('M', ['MM', 2], 'month')\naddFormatToken(0, ['YY', 2], function () { return this.year % 100 })\naddFormatToken('Y', ['YYYY', 4], 'year')\naddFormatToken('z', 0, function () { return this.zone.abbreviation })\n\nfunction addTimeZoneFormatToken (token, separator) {\n  addFormatToken(token, 0, function () {\n    let offset = -this.zone.offset\n    const sign = offset < 0 ? '-' : '+'\n    offset = Math.abs(offset)\n    return sign + padWithZeros(Math.floor(offset / 60), 2) + separator + padWithZeros(offset % 60, 2)\n  })\n}\n\naddTimeZoneFormatToken('Z', ':')\naddTimeZoneFormatToken('ZZ', '')\n\nfunction formatZonedTime (time, format) {\n  let formatter = formatters[format]\n  if (!formatter) {\n    formatter = formatters[format] = makeFormatter(format)\n  }\n  return formatter(time)\n}\n\nexport { formatZonedTime }\n"], "names": ["match1", "match2", "match1to2", "matchOffset", "parseTokenExpressions", "parseTokenFunctions", "parsers", "addExpressionToken", "token", "regex", "addParseToken", "tokens", "property", "callback", "input", "afternoon", "factor", "milliseconds", "year", "this", "zone", "abbreviation", "parts", "minutes", "offset", "match", "padToN", "undefined", "number", "padWithZeros", "length", "formatTokenFunctions", "formatters", "addFormatToken", "padded", "call", "addTimeZoneFormatToken", "separator", "sign", "Math", "abs", "floor", "hours", "format", "parser", "array", "formattingTokens", "Error", "i", "replace", "time", "start", "indexOf", "part", "substr", "exec", "index", "value", "correctD<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "formatter", "output", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "iNAAe,mFCETA,EAAS,KACTC,EAAS,OAGTC,EAAY,QAIZC,EAAc,iBAGdC,EAAwB,GACxBC,EAAsB,GACtBC,EAAU,GA8DhB,SAASC,EAAoBC,EAAOC,GAClCL,EAAsBI,GAASC,EAGjC,SAASC,EAAeC,EAAQC,GACR,iBAAXD,IACTA,EAAS,CAAEA,QAEPE,EAA+B,iBAAbD,EAAwB,SAAUE,QACnDF,IAAaE,GAChBF,IACcD,0DAAQ,qFACxBN,KAA6BQ,GAUjCN,EAAmB,IA5FI,SA6FvBG,EAAc,CAAC,KAAM,SAAUI,QACxBC,UAAsB,OAAVD,IAGnBP,EAAmB,IAhGI,SAiGvBG,EAAc,CAAC,KAAM,SAAUI,QACxBC,UAAsB,OAAVD,IAGnBP,EAAmB,IAAKP,GACxBO,EAAmB,KAAMN,GACzBM,EAAmB,MA3GJ,SA4Gf,mBAASC,EAAaQ,GACpBN,EAAcF,EAAO,SAAUM,QACxBG,cAAgBH,EAAQE,KAFxBR,EAAQ,IAAKQ,EAAS,IAAe,GAAVA,EAAaR,GAAS,IAAKQ,GAAU,KAAhER,EAAaQ,GAMtBT,EAAmB,IAAKL,GACxBK,EAAmB,KAAMN,GACzBS,EAAc,CAAC,IAAK,MAAO,WAE3BH,EAAmB,IAAKL,GACxBK,EAAmB,KAAMN,GACzBS,EAAc,CAAC,IAAK,MAAO,WAE3BH,EAAmB,IAAKL,GACxBK,EAAmB,IAAKL,GACxBK,EAAmB,KAAMN,GACzBM,EAAmB,KAAMN,GACzBS,EAAc,CAAC,IAAK,KAAM,IAAK,MAAO,SAEtCH,EAAmB,IAAKP,GACxBU,EAAc,IAAK,aAEnBH,EAAmB,IAAKL,GACxBK,EAAmB,KAAMN,GACzBS,EAAc,CAAC,IAAK,MAAO,OAE3BH,EAAmB,IAAKL,GACxBK,EAAmB,KAAMN,GACzBS,EAAc,CAAC,IAAK,MAAO,SAE3BH,EAAmB,IAtIC,YAuIpBA,EAAmB,KAAMN,GACzBM,EAAmB,OA5IJ,SA6IfG,EAAc,CAAC,IAAK,QAAS,QAC7BA,EAAc,KAAM,SAAUI,GAC5BA,GAASA,OACJI,KAAOJ,GAAiB,GAARA,EAAa,KAAO,OAG3CP,EAAmB,IA7IO,cA8I1BG,EAAc,IAAK,SAAUI,IACdK,KAAKC,OAASD,KAAKC,KAAO,KAClCC,aAAeP,IAGtBP,EAAmB,IAAKJ,GACxBI,EAAmB,KAAMJ,GACzBO,EAAc,CAAC,IAAK,MAAO,SAAUI,OAlE7BQ,EACAC,GAkEOJ,KAAKC,OAASD,KAAKC,KAAO,KAClCI,QApECF,EAoEyBR,EApEVW,MAAM,gBAER,KADbF,EAAuB,GAAXD,EAAM,KAAYA,EAAM,IACnB,EAAiB,MAAbA,EAAM,IAAcC,EAAUA,KCpF3D,IAAMG,EAAS,MAAEC,OAAWA,EAZ5B,SAAmBC,UACD,EAATA,EAAaA,EAAS,IAAMA,GAGrC,SAAqBA,UACH,GAATA,EAAcA,EAAkB,EAATA,EAAa,IAAMA,EAAS,KAAOA,GAGnE,SAAoBA,UACF,IAATA,EAAeA,EAAkB,GAATA,EAAc,IAAMA,EAAkB,EAATA,EAAa,KAAOA,EAAS,MAAQA,IAKnG,SAASC,EAAcD,EAAQE,UACtBJ,EAAOI,GAAQF,GCZxB,IAAMG,EAAuB,GACvBC,EAAa,GAuBnB,IAAMC,EAAiB,SAAUzB,EAAO0B,EAAQtB,OACxCC,EAA+B,iBAAbD,EAAwB,kBACvCO,KAAKP,IACVA,EACAJ,IACFuB,EAAqBvB,GAASK,GAE5BqB,IACFH,EAAqBG,EAAO,IAAM,kBACzBL,EAAahB,EAASsB,KAAKhB,MAAOe,EAAO,OAqBtD,SAASE,EAAwB5B,EAAO6B,GACtCJ,EAAezB,EAAO,EAAG,eACnBgB,GAAUL,KAAKC,KAAKI,OAClBc,EAAOd,EAAS,EAAI,IAAM,WAChCA,EAASe,KAAKC,IAAIhB,GACXc,EAAOT,EAAaU,KAAKE,MAAMjB,EAAS,IAAK,GAAKa,EAAYR,EAAaL,EAAS,GAAI,KArBnGS,EAAe,IAAK,EAAG,kBAAqBd,KAAKuB,MAAQ,GAAK,KAAO,OACrET,EAAe,IAAK,EAAG,kBAAqBd,KAAKuB,MAAQ,GAAK,KAAO,OACrET,EAAe,IAAK,EAAG,kBAAqBM,KAAKE,MAAMtB,KAAKF,aAAe,OAC3EgB,EAAe,EAAG,CAAC,KAAM,GAAI,kBAAqBM,KAAKE,MAAMtB,KAAKF,aAAe,MACjFgB,EAAe,EAAG,CAAC,MAAO,GAAI,gBAC9BA,EAAe,IAAK,CAAC,KAAM,GAAI,WAC/BA,EAAe,IAAK,CAAC,KAAM,GAAI,WAC/BA,EAAe,IAAK,CAAC,KAAM,GAAI,kBAAsBd,KAAKuB,MAAQ,IAAO,KACzET,EAAe,IAAK,CAAC,KAAM,GAAI,SAC/BA,EAAe,IAAK,EAAG,aACvBA,EAAe,IAAK,CAAC,KAAM,GAAI,OAC/BA,EAAe,IAAK,CAAC,KAAM,GAAI,SAC/BA,EAAe,EAAG,CAAC,KAAM,GAAI,kBAAqBd,KAAKD,KAAO,MAC9De,EAAe,IAAK,CAAC,OAAQ,GAAI,QACjCA,EAAe,IAAK,EAAG,kBAAqBd,KAAKC,KAAKC,eAWtDe,EAAuB,IAAK,KAC5BA,EAAuB,KAAM,qBFkG7B,SAAyBtB,EAAO6B,OAC1BC,EAAStC,EAAQqC,UAChBC,IACHA,EAAStC,EAAQqC,GAtIrB,SAAqBA,OACbE,EAAQF,EAAOlB,MAAMqB,OACtBD,QACG,IAAIE,0BAA0BJ,gBAE9Bb,EAAWe,EAAXf,OACCkB,EAAI,EAAGA,EAAIlB,IAAUkB,EAAG,KACzBxC,EAAQqC,EAAMG,GACdvC,EAAQL,EAAsBI,GAC9BoC,EAASvC,EAAoBG,GAEjCqC,EAAMG,GADJJ,EACS,CAAEnC,MAAAA,EAAOmC,OAAAA,GAETpC,EAAMyC,QAAQ,WAAY,WAGlC,SAAUnC,WACToC,EAAO,GACJF,EAAI,EAAGG,EAAQ,EAAGH,EAAIlB,IAAUkB,EAAG,KACpCxC,EAAQqC,EAAMG,MACC,iBAAVxC,EAAoB,IACzBM,EAAMsC,QAAQ5C,EAAO2C,KAAWA,EAAO,KACnCE,EAAOvC,EAAMwC,OAAOH,EAAO3C,EAAMsB,cACjC,IAAIiB,mBAAmBvC,oBAAuB2C,cAAiBE,QAEvEF,GAAS3C,EAAMsB,WACV,KACGrB,EAAkBD,EAAlBC,MAAOmC,EAAWpC,EAAXoC,OACTS,EAAOvC,EAAMwC,OAAOH,GACpB1B,EAAQhB,EAAM8C,KAAKF,OACpB5B,GAAyB,IAAhBA,EAAM+B,YACZ,IAAIT,mBAAmBtC,oBAAuB0C,mBAAsBE,YAEtEI,EAAQhC,EAAM,GACpBmB,EAAOT,KAAKe,EAAMO,GAClBN,GAASM,EAAM3B,eApDvB,SAA2BoB,OACjBnC,EAAcmC,EAAdnC,kBACUY,IAAdZ,EAAyB,KACnB2B,EAAUQ,EAAVR,MACJ3B,EACE2B,EAAQ,KACVQ,EAAKR,OAAS,IAGF,KAAVA,IACFQ,EAAKR,MAAQ,UAGVQ,EAAKnC,WA0CZ2C,CAAiBR,GACVA,GA+FoBS,CAAWhB,IAEjCC,EAAO9B,sBErGhB,SAA0BoC,EAAMP,OAC1BiB,EAAY5B,EAAWW,UACtBiB,IACHA,EAAY5B,EAAWW,GAlE3B,SAAwBA,WAChBE,EAAQF,EAAOlB,MAAMqB,GACnBhB,EAAWe,EAAXf,OACCkB,EAAI,EAAGA,EAAIlB,IAAUkB,EAAG,KACzBxC,EAAQqC,EAAMG,GACdY,EAAY7B,EAAqBvB,GAErCqC,EAAMG,GADJY,GAGSpD,EAAMyC,QAAQ,WAAY,WAGlC,SAAUC,OACXW,EAAS,KACKhB,0DAAO,yFAAhBrC,IACPqD,GAA2B,mBAAVrD,EAAuBA,EAAM2B,KAAKe,GAAQ1C,SAEtDqD,GAiD0BC,CAAcnB,IAE1CiB,EAAUV"}