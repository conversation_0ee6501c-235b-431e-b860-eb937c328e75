.TH "SCOPE" "7" "June 2025" "NPM@10.9.3" ""
.SH "NAME"
\fBscope\fR - Scoped packages
.SS "Description"
.P
All npm packages have a name. Some package names also have a scope. A scope follows the usual rules for package names (URL-safe characters, no leading dots or underscores). When used in package names, scopes are preceded by an \fB@\fR symbol and followed by a slash, e.g.
.P
.RS 2
.nf
@somescope/somepackagename
.fi
.RE
.P
Scopes are a way of grouping related packages together, and also affect a few things about the way npm treats the package.
.P
Each npm user/organization has their own scope, and only you can add packages in your scope. This means you don't have to worry about someone taking your package name ahead of you. Thus it is also a good way to signal official packages for organizations.
.P
Scoped packages can be published and installed as of \fBnpm@2\fR and are supported by the primary npm registry. Unscoped packages can depend on scoped packages and vice versa. The npm client is backwards-compatible with unscoped registries, so it can be used to work with scoped and unscoped registries at the same time.
.SS "Installing scoped packages"
.P
Scoped packages are installed to a sub-folder of the regular installation folder, e.g. if your other packages are installed in \fBnode_modules/packagename\fR, scoped modules will be installed in \fBnode_modules/@myorg/packagename\fR. The scope folder (\fB@myorg\fR) is simply the name of the scope preceded by an \fB@\fR symbol, and can contain any number of scoped packages.
.P
A scoped package is installed by referencing it by name, preceded by an \fB@\fR symbol, in \fBnpm install\fR:
.P
.RS 2
.nf
npm install @myorg/mypackage
.fi
.RE
.P
Or in \fBpackage.json\fR:
.P
.RS 2
.nf
"dependencies": {
  "@myorg/mypackage": "^1.3.0"
}
.fi
.RE
.P
Note that if the \fB@\fR symbol is omitted, in either case, npm will instead attempt to install from GitHub; see npm help install.
.SS "Requiring scoped packages"
.P
Because scoped packages are installed into a scope folder, you have to include the name of the scope when requiring them in your code, e.g.
.P
.RS 2
.nf
require('@myorg/mypackage')
.fi
.RE
.P
There is nothing special about the way Node treats scope folders. This simply requires the \fBmypackage\fR module in the folder named \fB@myorg\fR.
.SS "Publishing scoped packages"
.P
Scoped packages can be published from the CLI as of \fBnpm@2\fR and can be published to any registry that supports them, including the primary npm registry.
.P
(As of 2015-04-19, and with npm 2.0 or better, the primary npm registry \fBdoes\fR support scoped packages.)
.P
If you wish, you may associate a scope with a registry; see below.
.SS "Publishing public scoped packages to the primary npm registry"
.P
Publishing to a scope, you have two options:
.RS 0
.IP \(bu 4
Publishing to your user scope (example: \fB@username/module\fR)
.IP \(bu 4
Publishing to an organization scope (example: \fB@org/module\fR)
.RE 0

.P
If publishing a public module to an organization scope, you must first either create an organization with the name of the scope that you'd like to publish to or be added to an existing organization with the appropriate permissions. For example, if you'd like to publish to \fB@org\fR, you would need to create the \fBorg\fR organization on npmjs.com prior to trying to publish.
.P
Scoped packages are not public by default. You will need to specify \fB--access public\fR with the initial \fBnpm publish\fR command. This will publish the package and set access to \fBpublic\fR as if you had run \fBnpm access public\fR after publishing. You do not need to do this when publishing new versions of an existing scoped package.
.SS "Publishing private scoped packages to the npm registry"
.P
To publish a private scoped package to the npm registry, you must have an \fBnpm Private Modules\fR \fI\(lahttps://docs.npmjs.com/private-modules/intro\(ra\fR account.
.P
You can then publish the module with \fBnpm publish\fR or \fBnpm publish
--access restricted\fR, and it will be present in the npm registry, with restricted access. You can then change the access permissions, if desired, with \fBnpm access\fR or on the npmjs.com website.
.SS "Associating a scope with a registry"
.P
Scopes can be associated with a separate registry. This allows you to seamlessly use a mix of packages from the primary npm registry and one or more private registries, such as \fBGitHub Packages\fR \fI\(lahttps://github.com/features/packages\(ra\fR or the open source \fBVerdaccio\fR \fI\(lahttps://verdaccio.org\(ra\fR project.
.P
You can associate a scope with a registry at login, e.g.
.P
.RS 2
.nf
npm login --registry=http://reg.example.com --scope=@myco
.fi
.RE
.P
Scopes have a many-to-one relationship with registries: one registry can host multiple scopes, but a scope only ever points to one registry.
.P
You can also associate a scope with a registry using \fBnpm config\fR:
.P
.RS 2
.nf
npm config set @myco:registry=http://reg.example.com
.fi
.RE
.P
Once a scope is associated with a registry, any \fBnpm install\fR for a package with that scope will request packages from that registry instead. Any \fBnpm publish\fR for a package name that contains the scope will be published to that registry instead.
.SS "See also"
.RS 0
.IP \(bu 4
npm help install
.IP \(bu 4
npm help publish
.IP \(bu 4
npm help access
.IP \(bu 4
npm help registry
.RE 0
