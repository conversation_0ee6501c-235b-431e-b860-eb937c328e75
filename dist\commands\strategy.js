"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@oclif/core");
class Strategy extends core_1.Command {
    constructor() {
        super(...arguments);
        this.sampleStrategies = [
            {
                name: 'Moving Average Crossover',
                returns: 12.5,
                sharpeRatio: 1.8,
                maxDrawdown: -8.2,
                winRate: 65.4,
            },
            {
                name: 'RSI Mean Reversion',
                returns: 8.7,
                sharpeRatio: 1.2,
                maxDrawdown: -12.1,
                winRate: 58.9,
            },
            {
                name: 'Momentum Strategy',
                returns: 15.3,
                sharpeRatio: 2.1,
                maxDrawdown: -15.6,
                winRate: 62.1,
            },
        ];
    }
    async run() {
        const { flags } = await this.parse(Strategy);
        if (flags.create) {
            this.log(`✅ Created new strategy: "${flags.create}"`);
            this.log('   Status: Draft');
            this.log('   Next steps: Configure parameters and run backtest');
            return;
        }
        if (flags.delete) {
            this.log(`❌ Deleted strategy: "${flags.delete}"`);
            return;
        }
        if (flags.backtest) {
            this.log(`🔄 Running backtest for: "${flags.backtest}"`);
            this.log('');
            this.log('⏳ Analyzing historical data...');
            this.log('📊 Calculating performance metrics...');
            this.log('✅ Backtest completed!');
            this.log('');
            const strategy = this.sampleStrategies.find(s => s.name.toLowerCase().includes(flags.backtest.toLowerCase())) || this.sampleStrategies[0];
            this.log('📈 Backtest Results:');
            this.log(`   Strategy: ${strategy.name}`);
            this.log(`   Total Returns: ${strategy.returns}%`);
            this.log(`   Sharpe Ratio: ${strategy.sharpeRatio}`);
            this.log(`   Max Drawdown: ${strategy.maxDrawdown}%`);
            this.log(`   Win Rate: ${strategy.winRate}%`);
            return;
        }
        this.log('📋 Available Strategies:');
        this.log('');
        this.log('Strategy Name'.padEnd(25) + 'Returns'.padEnd(10) + 'Sharpe'.padEnd(8) + 'Win Rate');
        this.log('─'.repeat(55));
        for (const strategy of this.sampleStrategies) {
            this.log(strategy.name.padEnd(25) +
                `${strategy.returns}%`.padEnd(10) +
                strategy.sharpeRatio.toString().padEnd(8) +
                `${strategy.winRate}%`);
        }
        this.log('');
        this.log('💡 Use --backtest "strategy name" to run a backtest');
    }
}
Strategy.description = 'Manage and backtest trading strategies';
Strategy.examples = [
    '<%= config.bin %> <%= command.id %> --list',
    '<%= config.bin %> <%= command.id %> --backtest "Moving Average"',
    '<%= config.bin %> <%= command.id %> --create "My Strategy"',
];
Strategy.flags = {
    help: core_1.Flags.help({ char: 'h' }),
    list: core_1.Flags.boolean({ char: 'l', description: 'List all strategies' }),
    backtest: core_1.Flags.string({ char: 'b', description: 'Backtest a strategy' }),
    create: core_1.Flags.string({ char: 'c', description: 'Create a new strategy' }),
    delete: core_1.Flags.string({ char: 'd', description: 'Delete a strategy' }),
};
exports.default = Strategy;
//# sourceMappingURL=strategy.js.map