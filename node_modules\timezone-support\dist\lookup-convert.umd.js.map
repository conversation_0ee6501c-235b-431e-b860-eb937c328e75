{"version": 3, "file": "lookup-convert.umd.js", "sources": ["../src/lookup/unpack.js", "../src/lookup/lookup.js", "../src/convert/utc-date.js", "../src/convert/convert.js"], "sourcesContent": ["function charCodeToInt (charCode) {\n  if (charCode > 96) {\n    return charCode - 87\n  } else if (charCode > 64) {\n    return charCode - 29\n  }\n  return charCode - 48\n}\n\nfunction unpackBase60 (string) {\n  const parts = string.split('.')\n  const whole = parts[0]\n  const fractional = parts[1] || ''\n  let multiplier = 1\n  let start = 0\n  let out = 0\n  let sign = 1\n  // handle negative numbers\n  if (string.charCodeAt(0) === 45) {\n    start = 1\n    sign = -1\n  }\n  // handle digits before the decimal\n  for (let i = start, length = whole.length; i < length; ++i) {\n    const num = charCodeToInt(whole.charCodeAt(i))\n    out = (60 * out) + num\n  }\n  // handle digits after the decimal\n  // istanbul ignore next\n  for (let i = 0, length = fractional.length; i < length; ++i) {\n    const num = charCodeToInt(fractional.charCodeAt(i))\n    multiplier = multiplier / 60\n    out += num * multiplier\n  }\n  return out * sign\n}\n\nfunction arrayToInt (array) {\n  for (let i = 0, length = array.length; i < length; ++i) {\n    array[i] = unpackBase60(array[i])\n  }\n}\n\nfunction intToUntil (array, length) {\n  for (let i = 0; i < length; ++i) {\n    array[i] = Math.round((array[i - 1] || 0) + (array[i] * 60000))\n  }\n  array[length - 1] = Infinity\n}\n\nfunction mapIndices (source, indices) {\n  const out = []\n  for (let i = 0, length = indices.length; i < length; ++i) {\n    out[i] = source[indices[i]]\n  }\n  return out\n}\n\nfunction unpack (string) {\n  const data = string.split('|')\n  let offsets = data[2].split(' ')\n  const indices = data[3].split('')\n  const untils = data[4].split(' ')\n\n  arrayToInt(offsets)\n  arrayToInt(indices)\n  arrayToInt(untils)\n  intToUntil(untils, indices.length)\n\n  const name = data[0]\n  const abbreviations = mapIndices((data[1].split(' ')), indices)\n  const population = data[5] | 0\n  offsets = mapIndices(offsets, indices)\n\n  return { name, abbreviations, offsets, untils, population }\n}\n\nexport { unpack }\n", "import { unpack } from './unpack'\n\nlet zones\nlet names\nlet links\nlet instances\n\nfunction populateTimeZones ({ zones: zoneData, links: linkData }) {\n  zones = {}\n  names = zoneData.map(packed => {\n    const name = packed.substr(0, packed.indexOf('|'))\n    zones[name] = packed\n    return name\n  })\n  links = linkData.reduce((result, packed) => {\n    const [ name, alias ] = packed.split('|')\n    result[alias] = name\n    return result\n  }, {})\n  instances = {}\n}\n\nfunction listTimeZones () {\n  return names.slice()\n}\n\nfunction findTimeZone (alias) {\n  const name = links[alias] || alias\n  let timeZone = instances[name]\n  if (!timeZone) {\n    const packed = zones[name]\n    if (!packed) {\n      throw new Error(`Unknown time zone \"${name}\".`)\n    }\n    timeZone = instances[name] = unpack(packed)\n  }\n  return timeZone\n}\n\nexport { populateTimeZones, listTimeZones, findTimeZone }\n", "function getUnixTimeFromUTC ({ year, month, day, hours, minutes, seconds = 0, milliseconds = 0 }) {\n  return Date.UTC(year, month - 1, day, hours, minutes, seconds, milliseconds)\n}\n\nfunction getDateFromTime ({ year, month, day, hours, minutes, seconds = 0, milliseconds = 0 }) {\n  return new Date(year, month - 1, day, hours, minutes, seconds, milliseconds)\n}\n\nfunction getUTCTime (date) {\n  const year = date.getUTCFullYear()\n  const month = date.getUTCMonth() + 1\n  const day = date.getUTCDate()\n  const dayOfWeek = date.getUTCDay()\n  const hours = date.getUTCHours()\n  const minutes = date.getUTCMinutes()\n  const seconds = date.getUTCSeconds() || 0\n  const milliseconds = date.getUTCMilliseconds() || 0\n  return { year, month, day, dayOfWeek, hours, minutes, seconds, milliseconds }\n}\n\nfunction getLocalTime (date) {\n  const year = date.getFullYear()\n  const month = date.getMonth() + 1\n  const day = date.getDate()\n  const dayOfWeek = date.getDay()\n  const hours = date.getHours()\n  const minutes = date.getMinutes()\n  const seconds = date.getSeconds() || 0\n  const milliseconds = date.getMilliseconds() || 0\n  return { year, month, day, dayOfWeek, hours, minutes, seconds, milliseconds }\n}\n\nfunction getDateTime (date, options) {\n  const { useUTC } = options || {}\n  let extract\n  if (useUTC === true) {\n    extract = getUTCTime\n  } else if (useUTC === false) {\n    extract = getLocalTime\n  } else {\n    throw new Error('Extract local or UTC date? Set useUTC option.')\n  }\n  return extract(date)\n}\n\nexport { getUnixTimeFromUTC, getDateFromTime, getUTCTime, getLocalTime, getDateTime }\n", "import { getUnixTimeFromUTC, getDateFromTime, getUTCTime, getLocalTime, getDateTime } from './utc-date'\n\nfunction findTransitionIndex (unixTime, timeZone) {\n  const { untils } = timeZone\n  for (let i = 0, length = untils.length; i < length; ++i) {\n    if (unixTime < untils[i]) {\n      return i\n    }\n  }\n}\n\nfunction getTransition (unixTime, timeZone) {\n  const transitionIndex = findTransitionIndex(unixTime, timeZone)\n  const abbreviation = timeZone.abbreviations[transitionIndex]\n  const offset = timeZone.offsets[transitionIndex]\n  return { abbreviation, offset }\n}\n\nfunction attachEpoch (time, unixTime) {\n  Object.defineProperty(time, 'epoch', { value: unixTime })\n}\n\nfunction getUTCOffset (date, timeZone) {\n  const unixTime = typeof date === 'number' ? date : date.getTime()\n  const { abbreviation, offset } = getTransition(unixTime, timeZone)\n  return { abbreviation, offset }\n}\n\nfunction getZonedTime (date, timeZone) {\n  const gotUnixTime = typeof date === 'number'\n  const unixTime = gotUnixTime ? date : date.getTime()\n  const { abbreviation, offset } = getTransition(unixTime, timeZone)\n  if (gotUnixTime || offset) {\n    date = new Date(unixTime - offset * 60000)\n  }\n  const time = getUTCTime(date)\n  time.zone = { abbreviation, offset }\n  attachEpoch(time, unixTime)\n  return time\n}\n\nfunction getUnixTime (time, timeZone) {\n  let { zone, epoch } = time\n  if (epoch) {\n    if (timeZone) {\n      throw new Error('Both epoch and other time zone specified. Omit the other one.')\n    }\n    return epoch\n  }\n  const unixTime = getUnixTimeFromUTC(time)\n  if (zone) {\n    if (timeZone) {\n      throw new Error('Both own and other time zones specified. Omit the other one.')\n    }\n  } else {\n    if (!timeZone) {\n      throw new Error('Missing other time zone.')\n    }\n    zone = getTransition(unixTime, timeZone)\n  }\n  return unixTime + zone.offset * 60000\n}\n\nfunction setTimeZone (time, timeZone, options) {\n  if (time instanceof Date) {\n    time = getDateTime(time, options)\n  } else {\n    const { year, month, day, hours, minutes, seconds = 0, milliseconds = 0 } = time\n    time = { year, month, day, hours, minutes, seconds, milliseconds }\n  }\n  const unixTime = getUnixTimeFromUTC(time)\n  const utcDate = new Date(unixTime)\n  time.dayOfWeek = utcDate.getUTCDay()\n  const { abbreviation, offset } = getTransition(unixTime, timeZone)\n  time.zone = { abbreviation, offset }\n  attachEpoch(time, unixTime + offset * 60000)\n  return time\n}\n\nfunction convertTimeToDate (time) {\n  const { epoch } = time\n  if (epoch !== undefined) {\n    return new Date(epoch)\n  }\n  const { offset } = time.zone || {}\n  if (offset === undefined) {\n    return getDateFromTime(time)\n  }\n  const unixTime = getUnixTimeFromUTC(time)\n  return new Date(unixTime + offset * 60000)\n}\n\nfunction convertDateToTime (date) {\n  const time = getLocalTime(date)\n  const match = /\\(([^)]+)\\)$/.exec(date.toTimeString())\n  time.zone = {\n    abbreviation: match ? match[1]\n      // istanbul ignore next\n      : '???',\n    offset: date.getTimezoneOffset()\n  }\n  attachEpoch(time, date.getTime())\n  return time\n}\n\nexport { getUTCOffset, getZonedTime, getUnixTime, setTimeZone, convertTimeToDate, convertDateToTime }\n"], "names": ["charCodeToInt", "charCode", "unpackBase60", "string", "parts", "split", "whole", "fractional", "multiplier", "start", "out", "sign", "charCodeAt", "i", "length", "arrayToInt", "array", "mapIndices", "source", "indices", "unpack", "data", "offsets", "untils", "Math", "round", "Infinity", "intToUntil", "name", "abbreviations", "population", "zones", "names", "links", "instances", "getUnixTimeFromUTC", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "Date", "UTC", "getUTCTime", "date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "dayOfWeek", "getUTCDay", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "getLocalTime", "getFullYear", "getMonth", "getDate", "getDay", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "getTransition", "unixTime", "timeZone", "transitionIndex", "findTransitionIndex", "abbreviation", "offset", "attachEpoch", "time", "Object", "defineProperty", "value", "zoneData", "linkData", "map", "packed", "substr", "indexOf", "reduce", "result", "slice", "alias", "Error", "getTime", "gotUnixTime", "zone", "epoch", "options", "extract", "useUTC", "getDateTime", "utcDate", "undefined", "match", "exec", "toTimeString", "getTimezoneOffset"], "mappings": "6MAAA,SAASA,EAAeC,UACP,GAAXA,EACKA,EAAW,GACE,GAAXA,EACFA,EAAW,GAEbA,EAAW,GAGpB,SAASC,EAAcC,OACfC,EAAQD,EAAOE,MAAM,KACrBC,EAAQF,EAAM,GACdG,EAAaH,EAAM,IAAM,GAC3BI,EAAa,EACbC,EAAQ,EACRC,EAAM,EACNC,EAAO,EAEkB,KAAzBR,EAAOS,WAAW,KAEpBD,IADAF,EAAQ,QAIL,IAAII,EAAIJ,EAAOK,EAASR,EAAMQ,OAAQD,EAAIC,IAAUD,EAAG,CAE1DH,EAAO,GAAKA,EADAV,EAAcM,EAAMM,WAAWC,QAKxC,IAAIA,EAAI,EAAGC,EAASP,EAAWO,OAAQD,EAAIC,IAAUD,EAAG,CAG3DH,GAFYV,EAAcO,EAAWK,WAAWC,KAChDL,GAA0B,WAGrBE,EAAMC,EAGf,SAASI,EAAYC,OACd,IAAIH,EAAI,EAAGC,EAASE,EAAMF,OAAQD,EAAIC,IAAUD,EACnDG,EAAMH,GAAKX,EAAac,EAAMH,IAWlC,SAASI,EAAYC,EAAQC,WACrBT,EAAM,GACHG,EAAI,EAAGC,EAASK,EAAQL,OAAQD,EAAIC,IAAUD,EACrDH,EAAIG,GAAKK,EAAOC,EAAQN,WAEnBH,EAGT,SAASU,EAAQjB,OACTkB,EAAOlB,EAAOE,MAAM,KACtBiB,EAAUD,EAAK,GAAGhB,MAAM,KACtBc,EAAUE,EAAK,GAAGhB,MAAM,IACxBkB,EAASF,EAAK,GAAGhB,MAAM,KAE7BU,EAAWO,GACXP,EAAWI,GACXJ,EAAWQ,GAvBb,SAAqBP,EAAOF,OACrB,IAAID,EAAI,EAAGA,EAAIC,IAAUD,EAC5BG,EAAMH,GAAKW,KAAKC,OAAOT,EAAMH,EAAI,IAAM,GAAiB,IAAXG,EAAMH,IAErDG,EAAMF,EAAS,GAAKY,EAAAA,EAoBpBC,CAAWJ,EAAQJ,EAAQL,YAErBc,EAAOP,EAAK,GACZQ,EAAgBZ,EAAYI,EAAK,GAAGhB,MAAM,KAAOc,GACjDW,EAAuB,EAAVT,EAAK,SAGjB,CAAEO,KAAAA,EAAMC,cAAAA,EAAeP,QAF9BA,EAAUL,EAAWK,EAASH,GAESI,OAAAA,EAAQO,WAAAA,GCxEjD,IAAIC,EACAC,EACAC,EACAC,ECLJ,SAASC,SAAsBC,IAAAA,KAAMC,IAAAA,MAAOC,IAAAA,IAAKC,IAAAA,MAAOC,IAAAA,YAASC,QAAAA,aAAU,QAAGC,aAAAA,aAAe,WACpFC,KAAKC,IAAIR,EAAMC,EAAQ,EAAGC,EAAKC,EAAOC,EAASC,EAASC,GAOjE,SAASG,EAAYC,SASZ,CAAEV,KARIU,EAAKC,iBAQHV,MAPDS,EAAKE,cAAgB,EAObV,IANVQ,EAAKG,aAMUC,UALTJ,EAAKK,YAKeZ,MAJxBO,EAAKM,cAI0BZ,QAH7BM,EAAKO,gBAGiCZ,QAFtCK,EAAKQ,iBAAmB,EAEuBZ,aAD1CI,EAAKS,sBAAwB,GAIpD,SAASC,EAAcV,SASd,CAAEV,KARIU,EAAKW,cAQHpB,MAPDS,EAAKY,WAAa,EAOVpB,IANVQ,EAAKa,UAMUT,UALTJ,EAAKc,SAKerB,MAJxBO,EAAKe,WAI0BrB,QAH7BM,EAAKgB,aAGiCrB,QAFtCK,EAAKiB,cAAgB,EAE0BrB,aAD1CI,EAAKkB,mBAAqB,GCjBjD,SAASC,EAAeC,EAAUC,OAC1BC,EAVR,SAA8BF,EAAUC,WAC9B5C,EAAW4C,EAAX5C,OACCV,EAAI,EAAGC,EAASS,EAAOT,OAAQD,EAAIC,IAAUD,KAChDqD,EAAW3C,EAAOV,UACbA,EAMawD,CAAoBH,EAAUC,SAG/C,CAAEG,aAFYH,EAAStC,cAAcuC,GAErBG,OADRJ,EAAS7C,QAAQ8C,IAIlC,SAASI,EAAaC,EAAMP,GAC1BQ,OAAOC,eAAeF,EAAM,QAAS,CAAEG,MAAOV,wBFZhD,gBAAqCW,IAAP9C,MAAwB+C,IAAP7C,MAC7CF,EAAQ,GACRC,EAAQ6C,EAASE,IAAI,SAAAC,OACbpD,EAAOoD,EAAOC,OAAO,EAAGD,EAAOE,QAAQ,aAC7CnD,EAAMH,GAAQoD,EACPpD,IAETK,EAAQ6C,EAASK,OAAO,SAACC,EAAQJ,SACPA,EAAO3E,MAAM,KAA7BuB,cACRwD,QAAgBxD,EACTwD,GACN,IACHlD,EAAY,oBAGd,kBACSF,EAAMqD,wBAGf,SAAuBC,OACf1D,EAAOK,EAAMqD,IAAUA,EACzBnB,EAAWjC,EAAUN,OACpBuC,EAAU,KACPa,EAASjD,EAAMH,OAChBoD,QACG,IAAIO,4BAA4B3D,QAExCuC,EAAWjC,EAAUN,GAAQR,EAAO4D,UAE/Bb,kBEdT,SAAuBrB,EAAMqB,SAEMF,EADA,iBAATnB,EAAoBA,EAAOA,EAAK0C,UACCrB,SAClD,CAAEG,eADDA,aACeC,SADDA,wBAIxB,SAAuBzB,EAAMqB,OACrBsB,EAA8B,iBAAT3C,EACrBoB,EAAWuB,EAAc3C,EAAOA,EAAK0C,YACVvB,EAAcC,EAAUC,GAAjDG,IAAAA,aAAcC,IAAAA,QAClBkB,GAAelB,KACjBzB,EAAO,IAAIH,KAAKuB,EAAoB,IAATK,QAEvBE,EAAO5B,EAAWC,UACxB2B,EAAKiB,KAAO,CAAEpB,aAAAA,EAAcC,OAAAA,GAC5BC,EAAYC,EAAMP,GACXO,iBAGT,SAAsBA,EAAMN,OACpBuB,EAAgBjB,EAAhBiB,KAAMC,EAAUlB,EAAVkB,SACRA,EAAO,IACLxB,QACI,IAAIoB,MAAM,wEAEXI,MAEHzB,EAAW/B,EAAmBsC,MAChCiB,MACEvB,QACI,IAAIoB,MAAM,oEAEb,KACApB,QACG,IAAIoB,MAAM,4BAElBG,EAAOzB,EAAcC,EAAUC,UAE1BD,EAAyB,IAAdwB,EAAKnB,sBAGzB,SAAsBE,EAAMN,EAAUyB,MAChCnB,aAAgB9B,KAClB8B,EDjCJ,SAAsB3B,EAAM8C,OAEtBC,EADIC,GAAWF,GAAW,IAAtBE,WAEO,IAAXA,EACFD,EAAUhD,MACL,CAAA,IAAe,IAAXiD,QAGH,IAAIP,MAAM,iDAFhBM,EAAUrC,SAILqC,EAAQ/C,GCuBNiD,CAAYtB,EAAMmB,OACpB,OACuEnB,EAApErC,IAAAA,KAAMC,IAAAA,MAAOC,IAAAA,IAAKC,IAAAA,MAAOC,IAAAA,YAASC,QAAAA,aAAU,QAAGC,aACvD+B,EAAO,CAAErC,KAAAA,EAAMC,MAAAA,EAAOC,IAAAA,EAAKC,MAAAA,EAAOC,QAAAA,EAASC,QAAAA,EAASC,wBADkB,SAGlEwB,EAAW/B,EAAmBsC,GAC9BuB,EAAU,IAAIrD,KAAKuB,GACzBO,EAAKvB,UAAY8C,EAAQ7C,kBACQc,EAAcC,EAAUC,GAAjDG,IAAAA,aAAcC,IAAAA,cACtBE,EAAKiB,KAAO,CAAEpB,aAAAA,EAAcC,OAAAA,GAC5BC,EAAYC,EAAMP,EAAoB,IAATK,GACtBE,uBAGT,SAA4BA,OAClBkB,EAAUlB,EAAVkB,cACMM,IAAVN,SACK,IAAIhD,KAAKgD,SD9EQvD,EAAMC,EAAOC,EAAKC,EAAOC,IAASC,ICgFpD8B,GAAWE,EAAKiB,MAAQ,IAAxBnB,eACO0B,IAAX1B,SDjFsBnC,KCkFDqC,GDlFCrC,KAAMC,IAAAA,MAAOC,IAAAA,IAAKC,IAAAA,MAAOC,IAAAA,YAASC,QAAAA,aAAU,QAAGC,aAClE,IAAIC,KAAKP,EAAMC,EAAQ,EAAGC,EAAKC,EAAOC,EAASC,aADkC,SCoFlFyB,EAAW/B,EAAmBsC,UAC7B,IAAI9B,KAAKuB,EAAoB,IAATK,wBAG7B,SAA4BzB,OACpB2B,EAAOjB,EAAaV,GACpBoD,EAAQ,eAAeC,KAAKrD,EAAKsD,uBACvC3B,EAAKiB,KAAO,CACVpB,aAAc4B,EAAQA,EAAM,GAExB,MACJ3B,OAAQzB,EAAKuD,qBAEf7B,EAAYC,EAAM3B,EAAK0C,WAChBf"}