<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-search</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-search----1093">
    <span>npm-search</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">Search for packages</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#json"><code>json</code></a></li><li><a href="#color"><code>color</code></a></li><li><a href="#parseable"><code>parseable</code></a></li><li><a href="#description2"><code>description</code></a></li><li><a href="#searchlimit"><code>searchlimit</code></a></li><li><a href="#searchopts"><code>searchopts</code></a></li><li><a href="#searchexclude"><code>searchexclude</code></a></li><li><a href="#registry"><code>registry</code></a></li><li><a href="#prefer-online"><code>prefer-online</code></a></li><li><a href="#prefer-offline"><code>prefer-offline</code></a></li><li><a href="#offline"><code>offline</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm search &lt;search term&gt; [&lt;search term&gt; ...]

aliases: find, s, se
</code></pre>
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>Search the registry for packages matching the search terms. <code>npm search</code>
performs a linear, incremental, lexically-ordered search through package
metadata for all files in the registry. If your terminal has color
support, it will further highlight the matches in the results.  This can
be disabled with the config item <code>color</code></p>
<p>Additionally, using the <code>--searchopts</code> and <code>--searchexclude</code> options
paired with more search terms will include and exclude further patterns.
The main difference between <code>--searchopts</code> and the standard search terms
is that the former does not highlight results in the output and you can
use them more fine-grained filtering. Additionally, you can add both of
these to your config to change default search filtering behavior.</p>
<p>Search also allows targeting of maintainers in search results, by prefixing
their npm username with <code>=</code>.</p>
<p>If a term starts with <code>/</code>, then it's interpreted as a regular expression
and supports standard JavaScript RegExp syntax. In this case search will
ignore a trailing <code>/</code> .  (Note you must escape or quote many regular
expression characters in most shells.)</p>
<h3 id="configuration">Configuration</h3>
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<h4 id="color"><code>color</code></h4>
<ul>
<li>Default: true unless the NO_COLOR environ is set to something other than '0'</li>
<li>Type: "always" or Boolean</li>
</ul>
<p>If false, never shows colors. If <code>"always"</code> then always shows colors. If
true, then only prints color codes for tty file descriptors.</p>
<h4 id="parseable"><code>parseable</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Output parseable results from commands that write to standard output. For
<code>npm search</code>, this will be tab-separated table format.</p>
<h4 id="description2"><code>description</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Show the description in <code>npm search</code></p>
<h4 id="searchlimit"><code>searchlimit</code></h4>
<ul>
<li>Default: 20</li>
<li>Type: Number</li>
</ul>
<p>Number of items to limit search results to. Will not apply at all to legacy
searches.</p>
<h4 id="searchopts"><code>searchopts</code></h4>
<ul>
<li>Default: ""</li>
<li>Type: String</li>
</ul>
<p>Space-separated options that are always passed to search.</p>
<h4 id="searchexclude"><code>searchexclude</code></h4>
<ul>
<li>Default: ""</li>
<li>Type: String</li>
</ul>
<p>Space-separated options that limit the results from search.</p>
<h4 id="registry"><code>registry</code></h4>
<ul>
<li>Default: "<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>"</li>
<li>Type: URL</li>
</ul>
<p>The base URL of the npm registry.</p>
<h4 id="prefer-online"><code>prefer-online</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, staleness checks for cached data will be forced, making the CLI
look for updates immediately even for fresh package data.</p>
<h4 id="prefer-offline"><code>prefer-offline</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, staleness checks for cached data will be bypassed, but missing data
will be requested from the server. To force full offline mode, use
<code>--offline</code>.</p>
<h4 id="offline"><code>offline</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Force offline mode: no network requests will be done during install. To
allow the CLI to fill in missing cache data, see <code>--prefer-offline</code>.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../commands/npm-view.html">npm view</a></li>
<li><a href="../commands/npm-cache.html">npm cache</a></li>
<li><a href="https://npm.im/npm-registry-fetch">https://npm.im/npm-registry-fetch</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-search.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>