<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-version</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-version----1093">
    <span>npm-version</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">Bump a package version</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#allow-same-version"><code>allow-same-version</code></a></li><li><a href="#commit-hooks"><code>commit-hooks</code></a></li><li><a href="#git-tag-version"><code>git-tag-version</code></a></li><li><a href="#json"><code>json</code></a></li><li><a href="#preid"><code>preid</code></a></li><li><a href="#sign-git-tag"><code>sign-git-tag</code></a></li><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#workspaces-update"><code>workspaces-update</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li></ul><li><a href="#description">Description</a></li><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm version [&lt;newversion&gt; | major | minor | patch | premajor | preminor | prepatch | prerelease | from-git]

alias: verison
</code></pre>
<h3 id="configuration">Configuration</h3>
<h4 id="allow-same-version"><code>allow-same-version</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Prevents throwing an error when <code>npm version</code> is used to set the new version
to the same value as the current version.</p>
<h4 id="commit-hooks"><code>commit-hooks</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Run git commit hooks when using the <code>npm version</code> command.</p>
<h4 id="git-tag-version"><code>git-tag-version</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Tag the commit when using the <code>npm version</code> command. Setting this to false
results in no commit being made at all.</p>
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<h4 id="preid"><code>preid</code></h4>
<ul>
<li>Default: ""</li>
<li>Type: String</li>
</ul>
<p>The "prerelease identifier" to use as a prefix for the "prerelease" part of
a semver. Like the <code>rc</code> in <code>1.2.0-rc.8</code>.</p>
<h4 id="sign-git-tag"><code>sign-git-tag</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, then the <code>npm version</code> command will tag the version using
<code>-s</code> to add a signature.</p>
<p>Note that git requires you to have set up GPG keys in your git configs for
this to work properly.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces-update"><code>workspaces-update</code></h4>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, the npm cli will run an update after operations that may
possibly change the workspaces installed to the <code>node_modules</code> folder.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h3 id="description">Description</h3>
<p>Run this in a package directory to bump the version and write the new data
back to <code>package.json</code>, <code>package-lock.json</code>, and, if present,
<code>npm-shrinkwrap.json</code>.</p>
<p>The <code>newversion</code> argument should be a valid semver string, a valid second
argument to <a href="https://github.com/npm/node-semver#functions">semver.inc</a> (one
of <code>patch</code>, <code>minor</code>, <code>major</code>, <code>prepatch</code>, <code>preminor</code>, <code>premajor</code>,
<code>prerelease</code>), or <code>from-git</code>. In the second case, the existing version will
be incremented by 1 in the specified field.  <code>from-git</code> will try to read
the latest git tag, and use that as the new npm version.</p>
<p>If run in a git repo, it will also create a version commit and tag.  This
behavior is controlled by <code>git-tag-version</code> (see below), and can be
disabled on the command line by running <code>npm --no-git-tag-version version</code>.
It will fail if the working directory is not clean, unless the <code>-f</code> or
<code>--force</code> flag is set.</p>
<p>If supplied with <code>-m</code> or <a href="../using-npm/config#message.html"><code>--message</code> config</a> option,
npm will use it as a commit message when creating a version commit.  If the
<code>message</code> config contains <code>%s</code> then that will be replaced with the resulting
version number. For example:</p>
<pre><code class="language-bash">npm version patch -m "Upgrade to %s for reasons"
</code></pre>
<p>If the <a href="../using-npm/config#sign-git-tag.html"><code>sign-git-tag</code> config</a> is set, then the
tag will be signed using the <code>-s</code> flag to git. Note that you must have a default
GPG key set up in your git config for this to work properly. For example:</p>
<pre><code class="language-bash">$ npm config set sign-git-tag true
$ npm version patch

You need a passphrase to unlock the secret key for
user: "isaacs (http://blog.izs.me/) &lt;<EMAIL>&gt;"
2048-bit RSA key, ID 6C481CF6, created 2010-08-31

Enter passphrase:
</code></pre>
<p>If <code>preversion</code>, <code>version</code>, or <code>postversion</code> are in the <code>scripts</code> property
of the package.json, they will be executed as part of running <code>npm version</code>.</p>
<p>The exact order of execution is as follows:</p>
<ol>
<li>Check to make sure the git working directory is clean before we get
started.  Your scripts may add files to the commit in future steps.
This step is skipped if the <code>--force</code> flag is set.</li>
<li>Run the <code>preversion</code> script. These scripts have access to the old
<code>version</code> in package.json.  A typical use would be running your full
test suite before deploying.  Any files you want added to the commit
should be explicitly added using <code>git add</code>.</li>
<li>Bump <code>version</code> in <code>package.json</code> as requested (<code>patch</code>, <code>minor</code>,
<code>major</code>, etc).</li>
<li>Run the <code>version</code> script. These scripts have access to the new <code>version</code>
in package.json (so they can incorporate it into file headers in
generated files for example).  Again, scripts should explicitly add
generated files to the commit using <code>git add</code>.</li>
<li>Commit and tag.</li>
<li>Run the <code>postversion</code> script. Use it to clean up the file system or
automatically push the commit and/or tag.</li>
</ol>
<p>Take the following example:</p>
<pre><code class="language-json">{
  "scripts": {
    "preversion": "npm test",
    "version": "npm run build &amp;&amp; git add -A dist",
    "postversion": "git push &amp;&amp; git push --tags &amp;&amp; rm -rf build/temp"
  }
}
</code></pre>
<p>This runs all your tests and proceeds only if they pass. Then runs your
<code>build</code> script, and adds everything in the <code>dist</code> directory to the commit.
After the commit, it pushes the new commit and tag up to the server, and
deletes the <code>build/temp</code> directory.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-init.html">npm init</a></li>
<li><a href="../commands/npm-run-script.html">npm run-script</a></li>
<li><a href="../using-npm/scripts.html">npm scripts</a></li>
<li><a href="../configuring-npm/package-json.html">package.json</a></li>
<li><a href="../using-npm/config.html">config</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-version.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>