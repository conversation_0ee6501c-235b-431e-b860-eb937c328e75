<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-publish</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-publish----1093">
    <span>npm-publish</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">Publish a package</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#files-included-in-package">Files included in package</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#tag"><code>tag</code></a></li><li><a href="#access"><code>access</code></a></li><li><a href="#dry-run"><code>dry-run</code></a></li><li><a href="#otp"><code>otp</code></a></li><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li><li><a href="#provenance"><code>provenance</code></a></li><li><a href="#provenance-file"><code>provenance-file</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm publish &lt;package-spec&gt;
</code></pre>
<h3 id="description">Description</h3>
<p>Publishes a package to the registry so that it can be installed by name.</p>
<p>By default npm will publish to the public registry. This can be
overridden by specifying a different default registry or using a
<a href="../using-npm/scope.html"><code>scope</code></a> in the name, combined with a
scope-configured registry (see
<a href="../configuring-npm/package-json.html"><code>package.json</code></a>).</p>
<p>A <code>package</code> is interpreted the same way as other commands (like
<code>npm install</code>) and can be:</p>
<ul>
<li>a) a folder containing a program described by a
<a href="../configuring-npm/package-json.html"><code>package.json</code></a> file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code>&lt;name&gt;@&lt;version&gt;</code> that is published on the registry (see
<a href="../using-npm/registry.html"><code>registry</code></a>) with (c)</li>
<li>e) a <code>&lt;name&gt;@&lt;tag&gt;</code> (see <a href="../commands/npm-dist-tag.html"><code>npm dist-tag</code></a>) that
points to (d)</li>
<li>f) a <code>&lt;name&gt;</code> that has a "latest" tag satisfying (e)</li>
<li>g) a <code>&lt;git remote url&gt;</code> that resolves to (a)</li>
</ul>
<p>The publish will fail if the package name and version combination already
exists in the specified registry.</p>
<p>Once a package is published with a given name and version, that specific
name and version combination can never be used again, even if it is removed
with <a href="../commands/npm-unpublish.html"><code>npm unpublish</code></a>.</p>
<p>As of <code>npm@5</code>, both a sha1sum and an integrity field with a sha512sum of the
tarball will be submitted to the registry during publication. Subsequent
installs will use the strongest supported algorithm to verify downloads.</p>
<p>Similar to <code>--dry-run</code> see <a href="../commands/npm-pack.html"><code>npm pack</code></a>, which figures
out the files to be included and packs them into a tarball to be uploaded
to the registry.</p>
<h3 id="files-included-in-package">Files included in package</h3>
<p>To see what will be included in your package, run <code>npm pack --dry-run</code>.  All
files are included by default, with the following exceptions:</p>
<ul>
<li>
<p>Certain files that are relevant to package installation and distribution
are always included.  For example, <code>package.json</code>, <code>README.md</code>,
<code>LICENSE</code>, and so on.</p>
</li>
<li>
<p>If there is a "files" list in
<a href="../configuring-npm/package-json.html"><code>package.json</code></a>, then only the files
specified will be included.  (If directories are specified, then they
will be walked recursively and their contents included, subject to the
same ignore rules.)</p>
</li>
<li>
<p>If there is a <code>.gitignore</code> or <code>.npmignore</code> file, then ignored files in
that and all child directories will be excluded from the package.  If
<em>both</em> files exist, then the <code>.gitignore</code> is ignored, and only the
<code>.npmignore</code> is used.</p>
<p><code>.npmignore</code> files follow the <a href="https://git-scm.com/book/en/v2/Git-Basics-Recording-Changes-to-the-Repository#_ignoring">same pattern
rules</a>
as <code>.gitignore</code> files</p>
</li>
<li>
<p>If the file matches certain patterns, then it will <em>never</em> be included,
unless explicitly added to the <code>"files"</code> list in <code>package.json</code>, or
un-ignored with a <code>!</code> rule in a <code>.npmignore</code> or <code>.gitignore</code> file.</p>
</li>
<li>
<p>Symbolic links are never included in npm packages.</p>
</li>
</ul>
<p>See <a href="../using-npm/developers.html"><code>developers</code></a> for full details on what's
included in the published package, as well as details on how the package is
built.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="tag"><code>tag</code></h4>
<ul>
<li>Default: "latest"</li>
<li>Type: String</li>
</ul>
<p>If you ask npm to install a package and don't tell it a specific version,
then it will install the specified tag.</p>
<p>It is the tag added to the package@version specified in the <code>npm dist-tag add</code> command, if no explicit tag is given.</p>
<p>When used by the <code>npm diff</code> command, this is the tag used to fetch the
tarball that will be compared with the local files by default.</p>
<p>If used in the <code>npm publish</code> command, this is the tag that will be added to
the package submitted to the registry.</p>
<h4 id="access"><code>access</code></h4>
<ul>
<li>Default: 'public' for new packages, existing packages it will not change the
current level</li>
<li>Type: null, "restricted", or "public"</li>
</ul>
<p>If you do not want your scoped package to be publicly viewable (and
installable) set <code>--access=restricted</code>.</p>
<p>Unscoped packages can not be set to <code>restricted</code>.</p>
<p>Note: This defaults to not changing the current access level for existing
packages. Specifying a value of <code>restricted</code> or <code>public</code> during publish will
change the access for an existing package the same way that <code>npm access set status</code> would.</p>
<h4 id="dry-run"><code>dry-run</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Indicates that you don't want npm to make any changes and that it should
only report what it would have done. This can be passed into any of the
commands that modify your local installation, eg, <code>install</code>, <code>update</code>,
<code>dedupe</code>, <code>uninstall</code>, as well as <code>pack</code> and <code>publish</code>.</p>
<p>Note: This is NOT honored by other network related commands, eg <code>dist-tags</code>,
<code>owner</code>, etc.</p>
<h4 id="otp"><code>otp</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or String</li>
</ul>
<p>This is a one-time password from a two-factor authenticator. It's needed
when publishing or changing package permissions with <code>npm access</code>.</p>
<p>If not set, and a registry response fails with a challenge for a one-time
password, npm will prompt on the command line for one.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="provenance"><code>provenance</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>When publishing from a supported cloud CI/CD system, the package will be
publicly linked to where it was built and published from.</p>
<p>This config can not be used with: <code>provenance-file</code></p>
<h4 id="provenance-file"><code>provenance-file</code></h4>
<ul>
<li>Default: null</li>
<li>Type: Path</li>
</ul>
<p>When publishing, the provenance bundle at the given path will be used.</p>
<p>This config can not be used with: <code>provenance</code></p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../using-npm/package-spec.html">package spec</a></li>
<li><a href="http://npm.im/npm-packlist">npm-packlist package</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../using-npm/scope.html">npm scope</a></li>
<li><a href="../commands/npm-adduser.html">npm adduser</a></li>
<li><a href="../commands/npm-owner.html">npm owner</a></li>
<li><a href="../commands/npm-deprecate.html">npm deprecate</a></li>
<li><a href="../commands/npm-dist-tag.html">npm dist-tag</a></li>
<li><a href="../commands/npm-pack.html">npm pack</a></li>
<li><a href="../commands/npm-profile.html">npm profile</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-publish.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>