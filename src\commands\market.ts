import { Command, Flags } from '@oclif/core';

interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
}

export default class Market extends Command {
  static override description = 'Get market data and quotes';

  static override examples = [
    '<%= config.bin %> <%= command.id %> --quote AAPL',
    '<%= config.bin %> <%= command.id %> --watchlist',
    '<%= config.bin %> <%= command.id %> --trending',
  ];

  static override flags = {
    help: Flags.help({ char: 'h' }),
    quote: Flags.string({ char: 'q', description: 'Get quote for a symbol' }),
    watchlist: Flags.boolean({ char: 'w', description: 'Show watchlist' }),
    trending: Flags.boolean({ char: 't', description: 'Show trending stocks' }),
    add: Flags.string({ char: 'a', description: 'Add symbol to watchlist' }),
    remove: Flags.string({ char: 'r', description: 'Remove symbol from watchlist' }),
  };

  private sampleData: MarketData[] = [
    {
      symbol: 'AAPL',
      price: 150.25,
      change: 2.15,
      changePercent: 1.45,
      volume: 45678900,
      high: 152.10,
      low: 148.50,
    },
    {
      symbol: 'GOOGL',
      price: 2750.80,
      change: -15.20,
      changePercent: -0.55,
      volume: 1234567,
      high: 2780.00,
      low: 2745.30,
    },
    {
      symbol: 'MSFT',
      price: 305.15,
      change: 3.45,
      changePercent: 1.14,
      volume: 23456789,
      high: 307.20,
      low: 302.80,
    },
    {
      symbol: 'TSLA',
      price: 245.67,
      change: -8.33,
      changePercent: -3.28,
      volume: 67890123,
      high: 255.00,
      low: 243.10,
    },
  ];

  async run(): Promise<void> {
    const { flags } = await this.parse(Market);

    if (flags.quote) {
      const symbol = flags.quote.toUpperCase();
      const data = this.sampleData.find(d => d.symbol === symbol) || this.sampleData[0]!;

      this.log(`📊 Quote for ${data.symbol}:`);
      this.log('');
      this.log(`Price: $${data.price.toFixed(2)}`);

      const changeColor = data.change >= 0 ? '🟢' : '🔴';
      const changeSign = data.change >= 0 ? '+' : '';
      this.log(`Change: ${changeColor} ${changeSign}$${data.change.toFixed(2)} (${changeSign}${data.changePercent.toFixed(2)}%)`);

      this.log(`Volume: ${data.volume.toLocaleString()}`);
      this.log(`Day Range: $${data.low.toFixed(2)} - $${data.high.toFixed(2)}`);
      return;
    }

    if (flags.add) {
      this.log(`✅ Added ${flags.add.toUpperCase()} to watchlist`);
      return;
    }

    if (flags.remove) {
      this.log(`❌ Removed ${flags.remove.toUpperCase()} from watchlist`);
      return;
    }

    if (flags.trending) {
      this.log('🔥 Trending Stocks:');
      this.log('');
      
      // Sort by absolute change percent for trending
      const trending = [...this.sampleData].sort((a, b) => 
        Math.abs(b.changePercent) - Math.abs(a.changePercent)
      );
      
      for (const stock of trending.slice(0, 3)) {
        const changeColor = stock.change >= 0 ? '🟢' : '🔴';
        const changeSign = stock.change >= 0 ? '+' : '';
        this.log(`${changeColor} ${stock.symbol}: $${stock.price.toFixed(2)} (${changeSign}${stock.changePercent.toFixed(2)}%)`);
      }
      return;
    }

    // Default behavior or --watchlist flag
    this.log('👀 Market Watchlist:');
    this.log('');
    
    this.log('Symbol'.padEnd(8) + 'Price'.padEnd(12) + 'Change'.padEnd(15) + 'Volume');
    this.log('─'.repeat(50));
    
    for (const data of this.sampleData) {
      const changeColor = data.change >= 0 ? '🟢' : '🔴';
      const changeSign = data.change >= 0 ? '+' : '';
      const changeStr = `${changeColor} ${changeSign}${data.changePercent.toFixed(2)}%`;
      
      this.log(
        data.symbol.padEnd(8) +
        `$${data.price.toFixed(2)}`.padEnd(12) +
        changeStr.padEnd(15) +
        data.volume.toLocaleString()
      );
    }
    
    this.log('');
    this.log('💡 Use --quote SYMBOL to get detailed quote information');
  }
}
