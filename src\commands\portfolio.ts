import { Command, Flags } from '@oclif/core';

interface Position {
  symbol: string;
  quantity: number;
  price: number;
  value: number;
}

export default class Portfolio extends Command {
  static override description = 'Manage your trading portfolio';

  static override examples = [
    '<%= config.bin %> <%= command.id %>',
    '<%= config.bin %> <%= command.id %> --add AAPL --quantity 100 --price 150.50',
    '<%= config.bin %> <%= command.id %> --list',
  ];

  static override flags = {
    help: Flags.help({ char: 'h' }),
    add: Flags.string({ char: 'a', description: 'Add a position (symbol)' }),
    quantity: Flags.integer({ char: 'q', description: 'Quantity of shares' }),
    price: Flags.string({ char: 'p', description: 'Price per share' }),
    list: Flags.boolean({ char: 'l', description: 'List all positions' }),
    remove: Flags.string({ char: 'r', description: 'Remove a position (symbol)' }),
  };

  private samplePositions: Position[] = [
    { symbol: 'AAPL', quantity: 100, price: 150.25, value: 15025 },
    { symbol: 'GOOGL', quantity: 50, price: 2750.80, value: 137540 },
    { symbol: 'MSFT', quantity: 75, price: 305.15, value: 22886.25 },
    { symbol: 'TSLA', quantity: 25, price: 245.67, value: 6141.75 },
  ];

  async run(): Promise<void> {
    const { flags } = await this.parse(Portfolio);

    if (flags.add && flags.quantity && flags.price) {
      const price = parseFloat(flags.price);
      const value = flags.quantity * price;
      
      this.log(`✅ Added position: ${flags.add}`);
      this.log(`   Quantity: ${flags.quantity}`);
      this.log(`   Price: $${price.toFixed(2)}`);
      this.log(`   Total Value: $${value.toFixed(2)}`);
      return;
    }

    if (flags.remove) {
      this.log(`❌ Removed position: ${flags.remove}`);
      return;
    }

    // Default behavior or --list flag
    this.log('📊 Current Portfolio:');
    this.log('');
    
    let totalValue = 0;
    
    this.log('Symbol'.padEnd(8) + 'Qty'.padEnd(8) + 'Price'.padEnd(12) + 'Value');
    this.log('─'.repeat(40));
    
    for (const position of this.samplePositions) {
      totalValue += position.value;
      this.log(
        position.symbol.padEnd(8) +
        position.quantity.toString().padEnd(8) +
        `$${position.price.toFixed(2)}`.padEnd(12) +
        `$${position.value.toFixed(2)}`
      );
    }
    
    this.log('─'.repeat(40));
    this.log(`Total Portfolio Value: $${totalValue.toFixed(2)}`);
  }
}
