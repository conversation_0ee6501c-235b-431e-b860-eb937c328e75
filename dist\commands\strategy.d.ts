import { Command } from '@oclif/core';
export default class Strategy extends Command {
    static description: string;
    static examples: string[];
    static flags: {
        help: import("@oclif/core/lib/interfaces").BooleanFlag<void>;
        list: import("@oclif/core/lib/interfaces").BooleanFlag<boolean>;
        backtest: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
        create: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
        delete: import("@oclif/core/lib/interfaces").OptionFlag<string | undefined, import("@oclif/core/lib/interfaces").CustomOptions>;
    };
    private sampleStrategies;
    run(): Promise<void>;
}
//# sourceMappingURL=strategy.d.ts.map