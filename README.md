# AlgoTrade Framework CLI

A simple algorithmic trading framework CLI built with TypeScript and Oclif.

## Features

- ✅ TypeScript configuration with strict type checking
- ✅ Oclif-powered CLI with multiple commands
- ✅ Portfolio management commands
- ✅ Market data and quotes
- ✅ Strategy backtesting simulation
- ✅ Development and production build scripts
- ✅ Clean project structure

## Project Structure

```
algotrade-framework/
├── src/
│   ├── index.ts          # CLI entry point
│   └── commands/         # CLI commands
│       ├── index.ts      # Main command
│       ├── portfolio.ts  # Portfolio management
│       ├── market.ts     # Market data
│       └── strategy.ts   # Strategy backtesting
├── dist/                 # Compiled JavaScript output (generated)
├── package.json          # Project dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── README.md            # This file
```

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

### CLI Usage

1. **Build and run the CLI**:
   ```bash
   npm run cli
   ```

2. **Show help**:
   ```bash
   npm run cli -- --help
   ```

3. **Portfolio management**:
   ```bash
   npm run cli -- portfolio
   npm run cli -- portfolio --add AAPL --quantity 100 --price 150.50
   ```

4. **Market data**:
   ```bash
   npm run cli -- market --quote AAPL
   npm run cli -- market --watchlist
   npm run cli -- market --trending
   ```

5. **Strategy backtesting**:
   ```bash
   npm run cli -- strategy --list
   npm run cli -- strategy --backtest "Moving Average"
   npm run cli -- strategy --create "My Strategy"
   ```

### Development

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Run directly**:
   ```bash
   node dist/index.js --help
   ```

### Other Commands

- **Clean build directory**:
  ```bash
  npm run clean
  ```

## CLI Commands

The CLI includes several commands that demonstrate:

### Main Command
- Welcome message and feature overview
- Flag handling (--name, --force)
- Help system integration

### Portfolio Command
- View current portfolio positions
- Add new positions with quantity and price
- Remove positions by symbol
- Calculate total portfolio value

### Market Command
- Get real-time quotes for symbols
- View market watchlist
- Show trending stocks
- Add/remove symbols from watchlist

### Strategy Command
- List available trading strategies
- Run strategy backtests with performance metrics
- Create and delete custom strategies
- Display results with returns, Sharpe ratio, drawdown, and win rate

## TypeScript Configuration

The project uses strict TypeScript settings including:

- Strict null checks
- No implicit any
- Strict function types
- No implicit returns
- And more strict type checking options

## Next Steps

You can extend this CLI by:

1. **Real API Integration**: Connect to actual market data APIs (Alpha Vantage, Yahoo Finance, etc.)
2. **Database Storage**: Add persistent storage for portfolios and strategies
3. **Advanced Strategies**: Implement real backtesting algorithms
4. **Configuration**: Add config files for API keys and settings
5. **Testing**: Add unit tests with Jest or Mocha
6. **Linting**: Set up ESLint for code quality
7. **Publishing**: Publish to npm for global installation
8. **Interactive Mode**: Add interactive prompts with inquirer
9. **Charts**: Add ASCII charts for price visualization
10. **Notifications**: Add alerts for price movements

## Global Installation

To install globally and use `algotrade` command anywhere:

```bash
npm install -g .
algotrade --help
```

Happy trading! 📈🚀
