# Simple TypeScript Project

A basic TypeScript project setup with essential configuration and a sample UserManager class.

## Features

- ✅ TypeScript configuration with strict type checking
- ✅ Development and production build scripts
- ✅ Hot reload development server
- ✅ Sample code with interfaces and classes
- ✅ Clean project structure

## Project Structure

```
algotrade-framework/
├── src/
│   └── index.ts          # Main application entry point
├── dist/                 # Compiled JavaScript output (generated)
├── package.json          # Project dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── README.md            # This file
```

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

### Development

1. **Run in development mode** (with hot reload):
   ```bash
   npm run dev
   ```

2. **Watch mode** (restarts on file changes):
   ```bash
   npm run watch
   ```

### Production

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Run the compiled version**:
   ```bash
   npm start
   ```

### Other Commands

- **Clean build directory**:
  ```bash
  npm run clean
  ```

## Sample Code

The project includes a simple `UserManager` class that demonstrates:

- TypeScript interfaces
- Class definitions with private properties
- Method implementations with type safety
- Array manipulation with type checking

## TypeScript Configuration

The project uses strict TypeScript settings including:

- Strict null checks
- No implicit any
- Strict function types
- No implicit returns
- And more strict type checking options

## Next Steps

You can extend this project by:

1. Adding more classes and interfaces
2. Installing additional dependencies
3. Adding unit tests with Jest or Mocha
4. Setting up linting with ESLint
5. Adding a web framework like Express.js
6. Implementing database connectivity

Happy coding! 🚀
