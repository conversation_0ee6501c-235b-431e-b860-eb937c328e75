{"version": 3, "file": "data-2012-2022.umd.js", "sources": ["../src/lookup/data-2012-2022.js"], "sourcesContent": ["export default {\n  \"version\": \"2018g\",\n  \"zones\": [\n    \"Africa/Abidjan|GMT|0|0||48e5\",\n    \"Africa/Nairobi|EAT|-30|0||47e5\",\n    \"Africa/Algiers|CET|-10|0||26e5\",\n    \"Africa/Lagos|WAT|-10|0||17e6\",\n    \"Africa/Maputo|CAT|-20|0||26e5\",\n    \"Africa/Cairo|EET EEST|-20 -30|01010|1M2m0 gL0 e10 mn0|15e6\",\n    \"Africa/Casablanca|+00 +01|0 -10|0101010101010101010101010101|1H3C0 wM0 co0 go0 1o00 s00 dA0 vc0 11A0 A00 e00 y00 11A0 uM0 e00 Dc0 11A0 s00 e00 IM0 WM0 mo0 gM0 LA0 WM0 jA0 e00|32e5\",\n    \"Europe/Paris|CET CEST|-10 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|11e6\",\n    \"Africa/Johannesburg|SAST|-20|0||84e5\",\n    \"Africa/Khartoum|EAT CAT|-30 -20|01|1Usl0|51e5\",\n    \"Africa/Sao_Tome|GMT WAT|0 -10|01|1UQN0\",\n    \"Africa/Tripoli|EET CET CEST|-20 -10 -20|0120|1IlA0 TA0 1o00|11e5\",\n    \"Africa/Windhoek|CAT WAT|-20 -10|0101010101010|1GQo0 11B0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0|32e4\",\n    \"America/Adak|HST HDT|a0 90|01010101010101010101010|1GIc0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|326\",\n    \"America/Anchorage|AKST AKDT|90 80|01010101010101010101010|1GIb0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|30e4\",\n    \"America/Santo_Domingo|AST|40|0||29e5\",\n    \"America/Araguaina|-03 -02|30 20|010|1IdD0 Lz0|14e4\",\n    \"America/Fortaleza|-03|30|0||34e5\",\n    \"America/Asuncion|-03 -04|30 40|01010101010101010101010|1GTf0 1cN0 17b0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0 19X0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0|28e5\",\n    \"America/Panama|EST|50|0||15e5\",\n    \"America/Mexico_City|CST CDT|60 50|01010101010101010101010|1GQw0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0|20e6\",\n    \"America/Bahia|-02 -03|20 30|01|1GCq0|27e5\",\n    \"America/Managua|CST|60|0||22e5\",\n    \"America/La_Paz|-04|40|0||19e5\",\n    \"America/Lima|-05|50|0||11e6\",\n    \"America/Denver|MST MDT|70 60|01010101010101010101010|1GI90 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|26e5\",\n    \"America/Campo_Grande|-03 -04|30 40|01010101010101010101010|1GCr0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0 1HB0 FX0 1HB0 IL0 1HB0 FX0 1HB0|77e4\",\n    \"America/Cancun|CST CDT EST|60 50 50|01010102|1GQw0 1nX0 14p0 1lb0 14p0 1lb0 Dd0|63e4\",\n    \"America/Caracas|-0430 -04|4u 40|01|1QMT0|29e5\",\n    \"America/Chicago|CST CDT|60 50|01010101010101010101010|1GI80 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|92e5\",\n    \"America/Chihuahua|MST MDT|70 60|01010101010101010101010|1GQx0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0|81e4\",\n    \"America/Phoenix|MST|70|0||42e5\",\n    \"America/Los_Angeles|PST PDT|80 70|01010101010101010101010|1GIa0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|15e6\",\n    \"America/New_York|EST EDT|50 40|01010101010101010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|21e6\",\n    \"America/Rio_Branco|-04 -05|40 50|01|1KLE0|31e4\",\n    \"America/Fort_Nelson|PST PDT MST|80 70 70|01010102|1GIa0 1zb0 Op0 1zb0 Op0 1zb0 Op0|39e2\",\n    \"America/Halifax|AST ADT|40 30|01010101010101010101010|1GI60 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|39e4\",\n    \"America/Godthab|-03 -02|30 20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|17e3\",\n    \"America/Grand_Turk|EST EDT AST|50 40 40|0101010121010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 5Ip0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|37e2\",\n    \"America/Havana|CST CDT|50 40|01010101010101010101010|1GQt0 1qM0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0|21e5\",\n    \"America/Metlakatla|PST AKST AKDT|80 90 80|0121212121212121|1PAa0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|14e2\",\n    \"America/Miquelon|-03 -02|30 20|01010101010101010101010|1GI50 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|61e2\",\n    \"America/Montevideo|-02 -03|20 30|01010101|1GI40 1o10 11z0 1o10 11z0 1o10 11z0|17e5\",\n    \"America/Noronha|-02|20|0||30e2\",\n    \"America/Port-au-Prince|EST EDT|50 40|010101010101010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 3iN0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|23e5\",\n    \"Antarctica/Palmer|-03 -04|30 40|010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0|40\",\n    \"America/Santiago|-03 -04|30 40|010101010101010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1zb0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0|62e5\",\n    \"America/Sao_Paulo|-02 -03|20 30|01010101010101010101010|1GCq0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0 1HB0 FX0 1HB0 IL0 1HB0 FX0 1HB0|20e6\",\n    \"Atlantic/Azores|-01 +00|10 0|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|25e4\",\n    \"America/St_Johns|NST NDT|3u 2u|01010101010101010101010|1GI5u 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0|11e4\",\n    \"Antarctica/Casey|+11 +08|-b0 -80|0101|1GAF0 blz0 3m10|10\",\n    \"Antarctica/Davis|+05 +07|-50 -70|01|1GAI0|70\",\n    \"Pacific/Port_Moresby|+10|-a0|0||25e4\",\n    \"Pacific/Guadalcanal|+11|-b0|0||11e4\",\n    \"Asia/Tashkent|+05|-50|0||23e5\",\n    \"Pacific/Auckland|NZDT NZST|-d0 -c0|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|14e5\",\n    \"Asia/Baghdad|+03|-30|0||66e5\",\n    \"Antarctica/Troll|+00 +02|0 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|40\",\n    \"Asia/Dhaka|+06|-60|0||16e6\",\n    \"Asia/Amman|EET EEST|-20 -30|010101010101010101010|1GPy0 4bX0 Dd0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 11A0 1o00|25e5\",\n    \"Asia/Kamchatka|+12|-c0|0||18e4\",\n    \"Asia/Baku|+04 +05|-40 -50|010101010|1GNA0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00|27e5\",\n    \"Asia/Bangkok|+07|-70|0||15e6\",\n    \"Asia/Barnaul|+07 +06|-70 -60|010|1N7v0 3rd0\",\n    \"Asia/Beirut|EET EEST|-20 -30|01010101010101010101010|1GNy0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0|22e5\",\n    \"Asia/Kuala_Lumpur|+08|-80|0||71e5\",\n    \"Asia/Kolkata|IST|-5u|0||15e6\",\n    \"Asia/Chita|+10 +08 +09|-a0 -80 -90|012|1N7s0 3re0|33e4\",\n    \"Asia/Ulaanbaatar|+08 +09|-80 -90|01010|1O8G0 1cJ0 1cP0 1cJ0|12e5\",\n    \"Asia/Shanghai|CST|-80|0||23e6\",\n    \"Asia/Colombo|+0530|-5u|0||22e5\",\n    \"Asia/Damascus|EET EEST|-20 -30|01010101010101010101010|1GPy0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0|26e5\",\n    \"Asia/Dili|+09|-90|0||19e4\",\n    \"Asia/Dubai|+04|-40|0||39e5\",\n    \"Asia/Famagusta|EET EEST +03|-20 -30 -30|0101010101201010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 15U0 2Ks0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0\",\n    \"Asia/Gaza|EET EEST|-20 -30|01010101010101010101010|1GPy0 1a00 1fA0 1cL0 1cN0 1nX0 1210 1nz0 1220 1qL0 WN0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1qL0 WN0 1qL0 WN0 1qL0|18e5\",\n    \"Asia/Hong_Kong|HKT|-80|0||73e5\",\n    \"Asia/Hovd|+07 +08|-70 -80|01010|1O8H0 1cJ0 1cP0 1cJ0|81e3\",\n    \"Asia/Irkutsk|+09 +08|-90 -80|01|1N7t0|60e4\",\n    \"Europe/Istanbul|EET EEST +03|-20 -30 -30|01010101012|1GNB0 1qM0 11A0 1o00 1200 1nA0 11A0 1tA0 U00 15w0|13e6\",\n    \"Asia/Jakarta|WIB|-70|0||31e6\",\n    \"Asia/Jayapura|WIT|-90|0||26e4\",\n    \"Asia/Jerusalem|IST IDT|-20 -30|01010101010101010101010|1GPA0 1aL0 1eN0 1oL0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0 W10 1rz0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0|81e4\",\n    \"Asia/Kabul|+0430|-4u|0||46e5\",\n    \"Asia/Karachi|PKT|-50|0||24e6\",\n    \"Asia/Kathmandu|+0545|-5J|0||12e5\",\n    \"Asia/Yakutsk|+10 +09|-a0 -90|01|1N7s0|28e4\",\n    \"Asia/Krasnoyarsk|+08 +07|-80 -70|01|1N7u0|10e5\",\n    \"Asia/Magadan|+12 +10 +11|-c0 -a0 -b0|012|1N7q0 3Cq0|95e3\",\n    \"Asia/Makassar|WITA|-80|0||15e5\",\n    \"Asia/Manila|PST|-80|0||24e6\",\n    \"Europe/Athens|EET EEST|-20 -30|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|35e5\",\n    \"Asia/Novosibirsk|+07 +06|-70 -60|010|1N7v0 4eN0|15e5\",\n    \"Asia/Omsk|+07 +06|-70 -60|01|1N7v0|12e5\",\n    \"Asia/Pyongyang|KST KST|-90 -8u|010|1P4D0 6BA0|29e5\",\n    \"Asia/Rangoon|+0630|-6u|0||48e5\",\n    \"Asia/Sakhalin|+11 +10|-b0 -a0|010|1N7r0 3rd0|58e4\",\n    \"Asia/Seoul|KST|-90|0||23e6\",\n    \"Asia/Srednekolymsk|+12 +11|-c0 -b0|01|1N7q0|35e2\",\n    \"Asia/Tehran|+0330 +0430|-3u -4u|01010101010101010101010|1GLUu 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0|14e6\",\n    \"Asia/Tokyo|JST|-90|0||38e6\",\n    \"Asia/Tomsk|+07 +06|-70 -60|010|1N7v0 3Qp0|10e5\",\n    \"Asia/Vladivostok|+11 +10|-b0 -a0|01|1N7r0|60e4\",\n    \"Asia/Yekaterinburg|+06 +05|-60 -50|01|1N7w0|14e5\",\n    \"Europe/Lisbon|WET WEST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|27e5\",\n    \"Atlantic/Cape_Verde|-01|10|0||50e4\",\n    \"Australia/Sydney|AEDT AEST|-b0 -a0|01010101010101010101010|1GQg0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0|40e5\",\n    \"Australia/Adelaide|ACDT ACST|-au -9u|01010101010101010101010|1GQgu 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0|11e5\",\n    \"Australia/Brisbane|AEST|-a0|0||20e5\",\n    \"Australia/Darwin|ACST|-9u|0||12e4\",\n    \"Australia/Eucla|+0845|-8J|0||368\",\n    \"Australia/Lord_Howe|+11 +1030|-b0 -au|01010101010101010101010|1GQf0 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu|347\",\n    \"Australia/Perth|AWST|-80|0||18e5\",\n    \"Pacific/Easter|-05 -06|50 60|010101010101010101010|1H3D0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1zb0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0|30e2\",\n    \"Europe/Dublin|GMT IST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|12e5\",\n    \"Etc/GMT-1|+01|-10|0|\",\n    \"Pacific/Fakaofo|+13|-d0|0||483\",\n    \"Pacific/Kiritimati|+14|-e0|0||51e2\",\n    \"Etc/GMT-2|+02|-20|0|\",\n    \"Pacific/Tahiti|-10|a0|0||18e4\",\n    \"Pacific/Niue|-11|b0|0||12e2\",\n    \"Etc/GMT+12|-12|c0|0|\",\n    \"Pacific/Galapagos|-06|60|0||25e3\",\n    \"Etc/GMT+7|-07|70|0|\",\n    \"Pacific/Pitcairn|-08|80|0||56\",\n    \"Pacific/Gambier|-09|90|0||125\",\n    \"Etc/UCT|UCT|0|0|\",\n    \"Etc/UTC|UTC|0|0|\",\n    \"Europe/Ulyanovsk|+04 +03|-40 -30|010|1N7y0 3rd0|13e5\",\n    \"Europe/London|GMT BST|0 -10|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|10e6\",\n    \"Europe/Chisinau|EET EEST|-20 -30|01010101010101010101010|1GNA0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0|67e4\",\n    \"Europe/Kaliningrad|+03 EET|-30 -20|01|1N7z0|44e4\",\n    \"Europe/Kirov|+04 +03|-40 -30|01|1N7y0|48e4\",\n    \"Europe/Moscow|MSK MSK|-40 -30|01|1N7y0|16e6\",\n    \"Europe/Saratov|+04 +03|-40 -30|010|1N7y0 5810\",\n    \"Europe/Simferopol|EET EEST MSK MSK|-20 -30 -40 -30|0101023|1GNB0 1qM0 11A0 1o00 11z0 1nW0|33e4\",\n    \"Europe/Volgograd|+04 +03|-40 -30|010|1N7y0 9Jd0|10e5\",\n    \"Pacific/Honolulu|HST|a0|0||37e4\",\n    \"MET|MET MEST|-10 -20|01010101010101010101010|1GNB0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0\",\n    \"Pacific/Chatham|+1345 +1245|-dJ -cJ|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|600\",\n    \"Pacific/Apia|+14 +13|-e0 -d0|01010101010101010101010|1GQe0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00|37e3\",\n    \"Pacific/Bougainville|+10 +11|-a0 -b0|01|1NwE0|18e4\",\n    \"Pacific/Fiji|+13 +12|-d0 -c0|01010101010101010101010|1Goe0 1Nc0 Ao0 1Q00 xz0 1SN0 uM0 1SM0 uM0 1VA0 s00 1VA0 s00 1VA0 s00 1VA0 uM0 1SM0 uM0 1VA0 s00 1VA0|88e4\",\n    \"Pacific/Guam|ChST|-a0|0||17e4\",\n    \"Pacific/Marquesas|-0930|9u|0||86e2\",\n    \"Pacific/Pago_Pago|SST|b0|0||37e2\",\n    \"Pacific/Norfolk|+1130 +11|-bu -b0|01|1PoCu|25e4\",\n    \"Pacific/Tongatapu|+13 +14|-d0 -e0|010|1S4d0 s00|75e3\"\n  ],\n  \"links\": [\n    \"Africa/Abidjan|Africa/Accra\",\n    \"Africa/Abidjan|Africa/Bamako\",\n    \"Africa/Abidjan|Africa/Banjul\",\n    \"Africa/Abidjan|Africa/Bissau\",\n    \"Africa/Abidjan|Africa/Conakry\",\n    \"Africa/Abidjan|Africa/Dakar\",\n    \"Africa/Abidjan|Africa/Freetown\",\n    \"Africa/Abidjan|Africa/Lome\",\n    \"Africa/Abidjan|Africa/Monrovia\",\n    \"Africa/Abidjan|Africa/Nouakchott\",\n    \"Africa/Abidjan|Africa/Ouagadougou\",\n    \"Africa/Abidjan|Africa/Timbuktu\",\n    \"Africa/Abidjan|America/Danmarkshavn\",\n    \"Africa/Abidjan|Atlantic/Reykjavik\",\n    \"Africa/Abidjan|Atlantic/St_Helena\",\n    \"Africa/Abidjan|Etc/GMT\",\n    \"Africa/Abidjan|Etc/GMT+0\",\n    \"Africa/Abidjan|Etc/GMT-0\",\n    \"Africa/Abidjan|Etc/GMT0\",\n    \"Africa/Abidjan|Etc/Greenwich\",\n    \"Africa/Abidjan|GMT\",\n    \"Africa/Abidjan|GMT+0\",\n    \"Africa/Abidjan|GMT-0\",\n    \"Africa/Abidjan|GMT0\",\n    \"Africa/Abidjan|Greenwich\",\n    \"Africa/Abidjan|Iceland\",\n    \"Africa/Algiers|Africa/Tunis\",\n    \"Africa/Cairo|Egypt\",\n    \"Africa/Casablanca|Africa/El_Aaiun\",\n    \"Africa/Johannesburg|Africa/Maseru\",\n    \"Africa/Johannesburg|Africa/Mbabane\",\n    \"Africa/Lagos|Africa/Bangui\",\n    \"Africa/Lagos|Africa/Brazzaville\",\n    \"Africa/Lagos|Africa/Douala\",\n    \"Africa/Lagos|Africa/Kinshasa\",\n    \"Africa/Lagos|Africa/Libreville\",\n    \"Africa/Lagos|Africa/Luanda\",\n    \"Africa/Lagos|Africa/Malabo\",\n    \"Africa/Lagos|Africa/Ndjamena\",\n    \"Africa/Lagos|Africa/Niamey\",\n    \"Africa/Lagos|Africa/Porto-Novo\",\n    \"Africa/Maputo|Africa/Blantyre\",\n    \"Africa/Maputo|Africa/Bujumbura\",\n    \"Africa/Maputo|Africa/Gaborone\",\n    \"Africa/Maputo|Africa/Harare\",\n    \"Africa/Maputo|Africa/Kigali\",\n    \"Africa/Maputo|Africa/Lubumbashi\",\n    \"Africa/Maputo|Africa/Lusaka\",\n    \"Africa/Nairobi|Africa/Addis_Ababa\",\n    \"Africa/Nairobi|Africa/Asmara\",\n    \"Africa/Nairobi|Africa/Asmera\",\n    \"Africa/Nairobi|Africa/Dar_es_Salaam\",\n    \"Africa/Nairobi|Africa/Djibouti\",\n    \"Africa/Nairobi|Africa/Juba\",\n    \"Africa/Nairobi|Africa/Kampala\",\n    \"Africa/Nairobi|Africa/Mogadishu\",\n    \"Africa/Nairobi|Indian/Antananarivo\",\n    \"Africa/Nairobi|Indian/Comoro\",\n    \"Africa/Nairobi|Indian/Mayotte\",\n    \"Africa/Tripoli|Libya\",\n    \"America/Adak|America/Atka\",\n    \"America/Adak|US/Aleutian\",\n    \"America/Anchorage|America/Juneau\",\n    \"America/Anchorage|America/Nome\",\n    \"America/Anchorage|America/Sitka\",\n    \"America/Anchorage|America/Yakutat\",\n    \"America/Anchorage|US/Alaska\",\n    \"America/Campo_Grande|America/Cuiaba\",\n    \"America/Chicago|America/Indiana/Knox\",\n    \"America/Chicago|America/Indiana/Tell_City\",\n    \"America/Chicago|America/Knox_IN\",\n    \"America/Chicago|America/Matamoros\",\n    \"America/Chicago|America/Menominee\",\n    \"America/Chicago|America/North_Dakota/Beulah\",\n    \"America/Chicago|America/North_Dakota/Center\",\n    \"America/Chicago|America/North_Dakota/New_Salem\",\n    \"America/Chicago|America/Rainy_River\",\n    \"America/Chicago|America/Rankin_Inlet\",\n    \"America/Chicago|America/Resolute\",\n    \"America/Chicago|America/Winnipeg\",\n    \"America/Chicago|CST6CDT\",\n    \"America/Chicago|Canada/Central\",\n    \"America/Chicago|US/Central\",\n    \"America/Chicago|US/Indiana-Starke\",\n    \"America/Chihuahua|America/Mazatlan\",\n    \"America/Chihuahua|Mexico/BajaSur\",\n    \"America/Denver|America/Boise\",\n    \"America/Denver|America/Cambridge_Bay\",\n    \"America/Denver|America/Edmonton\",\n    \"America/Denver|America/Inuvik\",\n    \"America/Denver|America/Ojinaga\",\n    \"America/Denver|America/Shiprock\",\n    \"America/Denver|America/Yellowknife\",\n    \"America/Denver|Canada/Mountain\",\n    \"America/Denver|MST7MDT\",\n    \"America/Denver|Navajo\",\n    \"America/Denver|US/Mountain\",\n    \"America/Fortaleza|America/Argentina/Buenos_Aires\",\n    \"America/Fortaleza|America/Argentina/Catamarca\",\n    \"America/Fortaleza|America/Argentina/ComodRivadavia\",\n    \"America/Fortaleza|America/Argentina/Cordoba\",\n    \"America/Fortaleza|America/Argentina/Jujuy\",\n    \"America/Fortaleza|America/Argentina/La_Rioja\",\n    \"America/Fortaleza|America/Argentina/Mendoza\",\n    \"America/Fortaleza|America/Argentina/Rio_Gallegos\",\n    \"America/Fortaleza|America/Argentina/Salta\",\n    \"America/Fortaleza|America/Argentina/San_Juan\",\n    \"America/Fortaleza|America/Argentina/San_Luis\",\n    \"America/Fortaleza|America/Argentina/Tucuman\",\n    \"America/Fortaleza|America/Argentina/Ushuaia\",\n    \"America/Fortaleza|America/Belem\",\n    \"America/Fortaleza|America/Buenos_Aires\",\n    \"America/Fortaleza|America/Catamarca\",\n    \"America/Fortaleza|America/Cayenne\",\n    \"America/Fortaleza|America/Cordoba\",\n    \"America/Fortaleza|America/Jujuy\",\n    \"America/Fortaleza|America/Maceio\",\n    \"America/Fortaleza|America/Mendoza\",\n    \"America/Fortaleza|America/Paramaribo\",\n    \"America/Fortaleza|America/Recife\",\n    \"America/Fortaleza|America/Rosario\",\n    \"America/Fortaleza|America/Santarem\",\n    \"America/Fortaleza|Antarctica/Rothera\",\n    \"America/Fortaleza|Atlantic/Stanley\",\n    \"America/Fortaleza|Etc/GMT+3\",\n    \"America/Halifax|America/Glace_Bay\",\n    \"America/Halifax|America/Goose_Bay\",\n    \"America/Halifax|America/Moncton\",\n    \"America/Halifax|America/Thule\",\n    \"America/Halifax|Atlantic/Bermuda\",\n    \"America/Halifax|Canada/Atlantic\",\n    \"America/Havana|Cuba\",\n    \"America/La_Paz|America/Boa_Vista\",\n    \"America/La_Paz|America/Guyana\",\n    \"America/La_Paz|America/Manaus\",\n    \"America/La_Paz|America/Porto_Velho\",\n    \"America/La_Paz|Brazil/West\",\n    \"America/La_Paz|Etc/GMT+4\",\n    \"America/Lima|America/Bogota\",\n    \"America/Lima|America/Guayaquil\",\n    \"America/Lima|Etc/GMT+5\",\n    \"America/Los_Angeles|America/Dawson\",\n    \"America/Los_Angeles|America/Ensenada\",\n    \"America/Los_Angeles|America/Santa_Isabel\",\n    \"America/Los_Angeles|America/Tijuana\",\n    \"America/Los_Angeles|America/Vancouver\",\n    \"America/Los_Angeles|America/Whitehorse\",\n    \"America/Los_Angeles|Canada/Pacific\",\n    \"America/Los_Angeles|Canada/Yukon\",\n    \"America/Los_Angeles|Mexico/BajaNorte\",\n    \"America/Los_Angeles|PST8PDT\",\n    \"America/Los_Angeles|US/Pacific\",\n    \"America/Los_Angeles|US/Pacific-New\",\n    \"America/Managua|America/Belize\",\n    \"America/Managua|America/Costa_Rica\",\n    \"America/Managua|America/El_Salvador\",\n    \"America/Managua|America/Guatemala\",\n    \"America/Managua|America/Regina\",\n    \"America/Managua|America/Swift_Current\",\n    \"America/Managua|America/Tegucigalpa\",\n    \"America/Managua|Canada/Saskatchewan\",\n    \"America/Mexico_City|America/Bahia_Banderas\",\n    \"America/Mexico_City|America/Merida\",\n    \"America/Mexico_City|America/Monterrey\",\n    \"America/Mexico_City|Mexico/General\",\n    \"America/New_York|America/Detroit\",\n    \"America/New_York|America/Fort_Wayne\",\n    \"America/New_York|America/Indiana/Indianapolis\",\n    \"America/New_York|America/Indiana/Marengo\",\n    \"America/New_York|America/Indiana/Petersburg\",\n    \"America/New_York|America/Indiana/Vevay\",\n    \"America/New_York|America/Indiana/Vincennes\",\n    \"America/New_York|America/Indiana/Winamac\",\n    \"America/New_York|America/Indianapolis\",\n    \"America/New_York|America/Iqaluit\",\n    \"America/New_York|America/Kentucky/Louisville\",\n    \"America/New_York|America/Kentucky/Monticello\",\n    \"America/New_York|America/Louisville\",\n    \"America/New_York|America/Montreal\",\n    \"America/New_York|America/Nassau\",\n    \"America/New_York|America/Nipigon\",\n    \"America/New_York|America/Pangnirtung\",\n    \"America/New_York|America/Thunder_Bay\",\n    \"America/New_York|America/Toronto\",\n    \"America/New_York|Canada/Eastern\",\n    \"America/New_York|EST5EDT\",\n    \"America/New_York|US/East-Indiana\",\n    \"America/New_York|US/Eastern\",\n    \"America/New_York|US/Michigan\",\n    \"America/Noronha|Atlantic/South_Georgia\",\n    \"America/Noronha|Brazil/DeNoronha\",\n    \"America/Noronha|Etc/GMT+2\",\n    \"America/Panama|America/Atikokan\",\n    \"America/Panama|America/Cayman\",\n    \"America/Panama|America/Coral_Harbour\",\n    \"America/Panama|America/Jamaica\",\n    \"America/Panama|EST\",\n    \"America/Panama|Jamaica\",\n    \"America/Phoenix|America/Creston\",\n    \"America/Phoenix|America/Dawson_Creek\",\n    \"America/Phoenix|America/Hermosillo\",\n    \"America/Phoenix|MST\",\n    \"America/Phoenix|US/Arizona\",\n    \"America/Rio_Branco|America/Eirunepe\",\n    \"America/Rio_Branco|America/Porto_Acre\",\n    \"America/Rio_Branco|Brazil/Acre\",\n    \"America/Santiago|Chile/Continental\",\n    \"America/Santo_Domingo|America/Anguilla\",\n    \"America/Santo_Domingo|America/Antigua\",\n    \"America/Santo_Domingo|America/Aruba\",\n    \"America/Santo_Domingo|America/Barbados\",\n    \"America/Santo_Domingo|America/Blanc-Sablon\",\n    \"America/Santo_Domingo|America/Curacao\",\n    \"America/Santo_Domingo|America/Dominica\",\n    \"America/Santo_Domingo|America/Grenada\",\n    \"America/Santo_Domingo|America/Guadeloupe\",\n    \"America/Santo_Domingo|America/Kralendijk\",\n    \"America/Santo_Domingo|America/Lower_Princes\",\n    \"America/Santo_Domingo|America/Marigot\",\n    \"America/Santo_Domingo|America/Martinique\",\n    \"America/Santo_Domingo|America/Montserrat\",\n    \"America/Santo_Domingo|America/Port_of_Spain\",\n    \"America/Santo_Domingo|America/Puerto_Rico\",\n    \"America/Santo_Domingo|America/St_Barthelemy\",\n    \"America/Santo_Domingo|America/St_Kitts\",\n    \"America/Santo_Domingo|America/St_Lucia\",\n    \"America/Santo_Domingo|America/St_Thomas\",\n    \"America/Santo_Domingo|America/St_Vincent\",\n    \"America/Santo_Domingo|America/Tortola\",\n    \"America/Santo_Domingo|America/Virgin\",\n    \"America/Sao_Paulo|Brazil/East\",\n    \"America/St_Johns|Canada/Newfoundland\",\n    \"Antarctica/Palmer|America/Punta_Arenas\",\n    \"Asia/Baghdad|Antarctica/Syowa\",\n    \"Asia/Baghdad|Asia/Aden\",\n    \"Asia/Baghdad|Asia/Bahrain\",\n    \"Asia/Baghdad|Asia/Kuwait\",\n    \"Asia/Baghdad|Asia/Qatar\",\n    \"Asia/Baghdad|Asia/Riyadh\",\n    \"Asia/Baghdad|Etc/GMT-3\",\n    \"Asia/Baghdad|Europe/Minsk\",\n    \"Asia/Bangkok|Asia/Ho_Chi_Minh\",\n    \"Asia/Bangkok|Asia/Novokuznetsk\",\n    \"Asia/Bangkok|Asia/Phnom_Penh\",\n    \"Asia/Bangkok|Asia/Saigon\",\n    \"Asia/Bangkok|Asia/Vientiane\",\n    \"Asia/Bangkok|Etc/GMT-7\",\n    \"Asia/Bangkok|Indian/Christmas\",\n    \"Asia/Dhaka|Antarctica/Vostok\",\n    \"Asia/Dhaka|Asia/Almaty\",\n    \"Asia/Dhaka|Asia/Bishkek\",\n    \"Asia/Dhaka|Asia/Dacca\",\n    \"Asia/Dhaka|Asia/Kashgar\",\n    \"Asia/Dhaka|Asia/Qyzylorda\",\n    \"Asia/Dhaka|Asia/Thimbu\",\n    \"Asia/Dhaka|Asia/Thimphu\",\n    \"Asia/Dhaka|Asia/Urumqi\",\n    \"Asia/Dhaka|Etc/GMT-6\",\n    \"Asia/Dhaka|Indian/Chagos\",\n    \"Asia/Dili|Etc/GMT-9\",\n    \"Asia/Dili|Pacific/Palau\",\n    \"Asia/Dubai|Asia/Muscat\",\n    \"Asia/Dubai|Asia/Tbilisi\",\n    \"Asia/Dubai|Asia/Yerevan\",\n    \"Asia/Dubai|Etc/GMT-4\",\n    \"Asia/Dubai|Europe/Samara\",\n    \"Asia/Dubai|Indian/Mahe\",\n    \"Asia/Dubai|Indian/Mauritius\",\n    \"Asia/Dubai|Indian/Reunion\",\n    \"Asia/Gaza|Asia/Hebron\",\n    \"Asia/Hong_Kong|Hongkong\",\n    \"Asia/Jakarta|Asia/Pontianak\",\n    \"Asia/Jerusalem|Asia/Tel_Aviv\",\n    \"Asia/Jerusalem|Israel\",\n    \"Asia/Kamchatka|Asia/Anadyr\",\n    \"Asia/Kamchatka|Etc/GMT-12\",\n    \"Asia/Kamchatka|Kwajalein\",\n    \"Asia/Kamchatka|Pacific/Funafuti\",\n    \"Asia/Kamchatka|Pacific/Kwajalein\",\n    \"Asia/Kamchatka|Pacific/Majuro\",\n    \"Asia/Kamchatka|Pacific/Nauru\",\n    \"Asia/Kamchatka|Pacific/Tarawa\",\n    \"Asia/Kamchatka|Pacific/Wake\",\n    \"Asia/Kamchatka|Pacific/Wallis\",\n    \"Asia/Kathmandu|Asia/Katmandu\",\n    \"Asia/Kolkata|Asia/Calcutta\",\n    \"Asia/Kuala_Lumpur|Asia/Brunei\",\n    \"Asia/Kuala_Lumpur|Asia/Kuching\",\n    \"Asia/Kuala_Lumpur|Asia/Singapore\",\n    \"Asia/Kuala_Lumpur|Etc/GMT-8\",\n    \"Asia/Kuala_Lumpur|Singapore\",\n    \"Asia/Makassar|Asia/Ujung_Pandang\",\n    \"Asia/Rangoon|Asia/Yangon\",\n    \"Asia/Rangoon|Indian/Cocos\",\n    \"Asia/Seoul|ROK\",\n    \"Asia/Shanghai|Asia/Chongqing\",\n    \"Asia/Shanghai|Asia/Chungking\",\n    \"Asia/Shanghai|Asia/Harbin\",\n    \"Asia/Shanghai|Asia/Macao\",\n    \"Asia/Shanghai|Asia/Macau\",\n    \"Asia/Shanghai|Asia/Taipei\",\n    \"Asia/Shanghai|PRC\",\n    \"Asia/Shanghai|ROC\",\n    \"Asia/Tashkent|Antarctica/Mawson\",\n    \"Asia/Tashkent|Asia/Aqtau\",\n    \"Asia/Tashkent|Asia/Aqtobe\",\n    \"Asia/Tashkent|Asia/Ashgabat\",\n    \"Asia/Tashkent|Asia/Ashkhabad\",\n    \"Asia/Tashkent|Asia/Atyrau\",\n    \"Asia/Tashkent|Asia/Dushanbe\",\n    \"Asia/Tashkent|Asia/Oral\",\n    \"Asia/Tashkent|Asia/Samarkand\",\n    \"Asia/Tashkent|Etc/GMT-5\",\n    \"Asia/Tashkent|Indian/Kerguelen\",\n    \"Asia/Tashkent|Indian/Maldives\",\n    \"Asia/Tehran|Iran\",\n    \"Asia/Tokyo|Japan\",\n    \"Asia/Ulaanbaatar|Asia/Choibalsan\",\n    \"Asia/Ulaanbaatar|Asia/Ulan_Bator\",\n    \"Asia/Vladivostok|Asia/Ust-Nera\",\n    \"Asia/Yakutsk|Asia/Khandyga\",\n    \"Atlantic/Azores|America/Scoresbysund\",\n    \"Atlantic/Cape_Verde|Etc/GMT+1\",\n    \"Australia/Adelaide|Australia/Broken_Hill\",\n    \"Australia/Adelaide|Australia/South\",\n    \"Australia/Adelaide|Australia/Yancowinna\",\n    \"Australia/Brisbane|Australia/Lindeman\",\n    \"Australia/Brisbane|Australia/Queensland\",\n    \"Australia/Darwin|Australia/North\",\n    \"Australia/Lord_Howe|Australia/LHI\",\n    \"Australia/Perth|Australia/West\",\n    \"Australia/Sydney|Australia/ACT\",\n    \"Australia/Sydney|Australia/Canberra\",\n    \"Australia/Sydney|Australia/Currie\",\n    \"Australia/Sydney|Australia/Hobart\",\n    \"Australia/Sydney|Australia/Melbourne\",\n    \"Australia/Sydney|Australia/NSW\",\n    \"Australia/Sydney|Australia/Tasmania\",\n    \"Australia/Sydney|Australia/Victoria\",\n    \"Etc/UCT|UCT\",\n    \"Etc/UTC|Etc/Universal\",\n    \"Etc/UTC|Etc/Zulu\",\n    \"Etc/UTC|UTC\",\n    \"Etc/UTC|Universal\",\n    \"Etc/UTC|Zulu\",\n    \"Europe/Athens|Asia/Nicosia\",\n    \"Europe/Athens|EET\",\n    \"Europe/Athens|Europe/Bucharest\",\n    \"Europe/Athens|Europe/Helsinki\",\n    \"Europe/Athens|Europe/Kiev\",\n    \"Europe/Athens|Europe/Mariehamn\",\n    \"Europe/Athens|Europe/Nicosia\",\n    \"Europe/Athens|Europe/Riga\",\n    \"Europe/Athens|Europe/Sofia\",\n    \"Europe/Athens|Europe/Tallinn\",\n    \"Europe/Athens|Europe/Uzhgorod\",\n    \"Europe/Athens|Europe/Vilnius\",\n    \"Europe/Athens|Europe/Zaporozhye\",\n    \"Europe/Chisinau|Europe/Tiraspol\",\n    \"Europe/Dublin|Eire\",\n    \"Europe/Istanbul|Asia/Istanbul\",\n    \"Europe/Istanbul|Turkey\",\n    \"Europe/Lisbon|Atlantic/Canary\",\n    \"Europe/Lisbon|Atlantic/Faeroe\",\n    \"Europe/Lisbon|Atlantic/Faroe\",\n    \"Europe/Lisbon|Atlantic/Madeira\",\n    \"Europe/Lisbon|Portugal\",\n    \"Europe/Lisbon|WET\",\n    \"Europe/London|Europe/Belfast\",\n    \"Europe/London|Europe/Guernsey\",\n    \"Europe/London|Europe/Isle_of_Man\",\n    \"Europe/London|Europe/Jersey\",\n    \"Europe/London|GB\",\n    \"Europe/London|GB-Eire\",\n    \"Europe/Moscow|W-SU\",\n    \"Europe/Paris|Africa/Ceuta\",\n    \"Europe/Paris|Arctic/Longyearbyen\",\n    \"Europe/Paris|Atlantic/Jan_Mayen\",\n    \"Europe/Paris|CET\",\n    \"Europe/Paris|Europe/Amsterdam\",\n    \"Europe/Paris|Europe/Andorra\",\n    \"Europe/Paris|Europe/Belgrade\",\n    \"Europe/Paris|Europe/Berlin\",\n    \"Europe/Paris|Europe/Bratislava\",\n    \"Europe/Paris|Europe/Brussels\",\n    \"Europe/Paris|Europe/Budapest\",\n    \"Europe/Paris|Europe/Busingen\",\n    \"Europe/Paris|Europe/Copenhagen\",\n    \"Europe/Paris|Europe/Gibraltar\",\n    \"Europe/Paris|Europe/Ljubljana\",\n    \"Europe/Paris|Europe/Luxembourg\",\n    \"Europe/Paris|Europe/Madrid\",\n    \"Europe/Paris|Europe/Malta\",\n    \"Europe/Paris|Europe/Monaco\",\n    \"Europe/Paris|Europe/Oslo\",\n    \"Europe/Paris|Europe/Podgorica\",\n    \"Europe/Paris|Europe/Prague\",\n    \"Europe/Paris|Europe/Rome\",\n    \"Europe/Paris|Europe/San_Marino\",\n    \"Europe/Paris|Europe/Sarajevo\",\n    \"Europe/Paris|Europe/Skopje\",\n    \"Europe/Paris|Europe/Stockholm\",\n    \"Europe/Paris|Europe/Tirane\",\n    \"Europe/Paris|Europe/Vaduz\",\n    \"Europe/Paris|Europe/Vatican\",\n    \"Europe/Paris|Europe/Vienna\",\n    \"Europe/Paris|Europe/Warsaw\",\n    \"Europe/Paris|Europe/Zagreb\",\n    \"Europe/Paris|Europe/Zurich\",\n    \"Europe/Paris|Poland\",\n    \"Europe/Ulyanovsk|Europe/Astrakhan\",\n    \"Pacific/Auckland|Antarctica/McMurdo\",\n    \"Pacific/Auckland|Antarctica/South_Pole\",\n    \"Pacific/Auckland|NZ\",\n    \"Pacific/Chatham|NZ-CHAT\",\n    \"Pacific/Easter|Chile/EasterIsland\",\n    \"Pacific/Fakaofo|Etc/GMT-13\",\n    \"Pacific/Fakaofo|Pacific/Enderbury\",\n    \"Pacific/Galapagos|Etc/GMT+6\",\n    \"Pacific/Gambier|Etc/GMT+9\",\n    \"Pacific/Guadalcanal|Antarctica/Macquarie\",\n    \"Pacific/Guadalcanal|Etc/GMT-11\",\n    \"Pacific/Guadalcanal|Pacific/Efate\",\n    \"Pacific/Guadalcanal|Pacific/Kosrae\",\n    \"Pacific/Guadalcanal|Pacific/Noumea\",\n    \"Pacific/Guadalcanal|Pacific/Pohnpei\",\n    \"Pacific/Guadalcanal|Pacific/Ponape\",\n    \"Pacific/Guam|Pacific/Saipan\",\n    \"Pacific/Honolulu|HST\",\n    \"Pacific/Honolulu|Pacific/Johnston\",\n    \"Pacific/Honolulu|US/Hawaii\",\n    \"Pacific/Kiritimati|Etc/GMT-14\",\n    \"Pacific/Niue|Etc/GMT+11\",\n    \"Pacific/Pago_Pago|Pacific/Midway\",\n    \"Pacific/Pago_Pago|Pacific/Samoa\",\n    \"Pacific/Pago_Pago|US/Samoa\",\n    \"Pacific/Pitcairn|Etc/GMT+8\",\n    \"Pacific/Port_Moresby|Antarctica/DumontDUrville\",\n    \"Pacific/Port_Moresby|Etc/GMT-10\",\n    \"Pacific/Port_Moresby|Pacific/Chuuk\",\n    \"Pacific/Port_Moresby|Pacific/Truk\",\n    \"Pacific/Port_Moresby|Pacific/Yap\",\n    \"Pacific/Tahiti|Etc/GMT+10\",\n    \"Pacific/Tahiti|Pacific/Rarotonga\"\n  ]\n}"], "names": [], "mappings": "4MAAe,SACF,cACF,CACP,+BACA,iCACA,iCACA,+BACA,gCACA,6DACA,sLACA,yKACA,uCACA,gDACA,yCACA,mEACA,iHACA,8JACA,sKACA,uCACA,qDACA,mCACA,6KACA,gCACA,gLACA,4CACA,iCACA,gCACA,8BACA,iKACA,uKACA,uFACA,gDACA,kKACA,8KACA,iCACA,sKACA,mKACA,iDACA,0FACA,kKACA,yKACA,uJACA,iKACA,uIACA,mKACA,qFACA,iCACA,+JACA,oFACA,2JACA,oKACA,wKACA,mKACA,2DACA,+CACA,uCACA,sCACA,gCACA,iLACA,+BACA,wKACA,6BACA,2JACA,iCACA,oFACA,+BACA,8CACA,wKACA,oCACA,+BACA,yDACA,mEACA,gCACA,iCACA,yKACA,4BACA,6BACA,yKACA,oKACA,iCACA,4DACA,6CACA,8GACA,+BACA,gCACA,0KACA,+BACA,+BACA,mCACA,6CACA,iDACA,2DACA,iCACA,8BACA,0KACA,uDACA,0CACA,qDACA,iCACA,oDACA,6BACA,mDACA,8KACA,6BACA,iDACA,iDACA,mDACA,wKACA,qCACA,iLACA,mLACA,sCACA,oCACA,mCACA,mLACA,mCACA,yJACA,uKACA,uBACA,iCACA,qCACA,uBACA,gCACA,8BACA,uBACA,mCACA,sBACA,gCACA,gCACA,mBACA,mBACA,uDACA,uKACA,4KACA,mDACA,6CACA,8CACA,gDACA,iGACA,uDACA,kCACA,2JACA,iLACA,2KACA,qDACA,iKACA,gCACA,qCACA,mCACA,kDACA,8DAEO,CACP,8BACA,+BACA,+BACA,+BACA,gCACA,8BACA,iCACA,6BACA,iCACA,mCACA,oCACA,iCACA,sCACA,oCACA,oCACA,yBACA,2BACA,2BACA,0BACA,+BACA,qBACA,uBACA,uBACA,sBACA,2BACA,yBACA,8BACA,qBACA,oCACA,oCACA,qCACA,6BACA,kCACA,6BACA,+BACA,iCACA,6BACA,6BACA,+BACA,6BACA,iCACA,gCACA,iCACA,gCACA,8BACA,8BACA,kCACA,8BACA,oCACA,+BACA,+BACA,sCACA,iCACA,6BACA,gCACA,kCACA,qCACA,+BACA,gCACA,uBACA,4BACA,2BACA,mCACA,iCACA,kCACA,oCACA,8BACA,sCACA,uCACA,4CACA,kCACA,oCACA,oCACA,8CACA,8CACA,iDACA,sCACA,uCACA,mCACA,mCACA,0BACA,iCACA,6BACA,oCACA,qCACA,mCACA,+BACA,uCACA,kCACA,gCACA,iCACA,kCACA,qCACA,iCACA,yBACA,wBACA,6BACA,mDACA,gDACA,qDACA,8CACA,4CACA,+CACA,8CACA,mDACA,4CACA,+CACA,+CACA,8CACA,8CACA,kCACA,yCACA,sCACA,oCACA,oCACA,kCACA,mCACA,oCACA,uCACA,mCACA,oCACA,qCACA,uCACA,qCACA,8BACA,oCACA,oCACA,kCACA,gCACA,mCACA,kCACA,sBACA,mCACA,gCACA,gCACA,qCACA,6BACA,2BACA,8BACA,iCACA,yBACA,qCACA,uCACA,2CACA,sCACA,wCACA,yCACA,qCACA,mCACA,uCACA,8BACA,iCACA,qCACA,iCACA,qCACA,sCACA,oCACA,iCACA,wCACA,sCACA,sCACA,6CACA,qCACA,wCACA,qCACA,mCACA,sCACA,gDACA,2CACA,8CACA,yCACA,6CACA,2CACA,wCACA,mCACA,+CACA,+CACA,sCACA,oCACA,kCACA,mCACA,uCACA,uCACA,mCACA,kCACA,2BACA,mCACA,8BACA,+BACA,yCACA,mCACA,4BACA,kCACA,gCACA,uCACA,iCACA,qBACA,yBACA,kCACA,uCACA,qCACA,sBACA,6BACA,sCACA,wCACA,iCACA,qCACA,yCACA,wCACA,sCACA,yCACA,6CACA,wCACA,yCACA,wCACA,2CACA,2CACA,8CACA,wCACA,2CACA,2CACA,8CACA,4CACA,8CACA,yCACA,yCACA,0CACA,2CACA,wCACA,uCACA,gCACA,uCACA,yCACA,gCACA,yBACA,4BACA,2BACA,0BACA,2BACA,yBACA,4BACA,gCACA,iCACA,+BACA,2BACA,8BACA,yBACA,gCACA,+BACA,yBACA,0BACA,wBACA,0BACA,4BACA,yBACA,0BACA,yBACA,uBACA,2BACA,sBACA,0BACA,yBACA,0BACA,0BACA,uBACA,2BACA,yBACA,8BACA,4BACA,wBACA,0BACA,8BACA,+BACA,wBACA,6BACA,4BACA,2BACA,kCACA,mCACA,gCACA,+BACA,gCACA,8BACA,gCACA,+BACA,6BACA,gCACA,iCACA,mCACA,8BACA,8BACA,mCACA,2BACA,4BACA,iBACA,+BACA,+BACA,4BACA,2BACA,2BACA,4BACA,oBACA,oBACA,kCACA,2BACA,4BACA,8BACA,+BACA,4BACA,8BACA,0BACA,+BACA,0BACA,iCACA,gCACA,mBACA,mBACA,mCACA,mCACA,iCACA,6BACA,uCACA,gCACA,2CACA,qCACA,0CACA,wCACA,0CACA,mCACA,oCACA,iCACA,iCACA,sCACA,oCACA,oCACA,uCACA,iCACA,sCACA,sCACA,cACA,wBACA,mBACA,cACA,oBACA,eACA,6BACA,oBACA,iCACA,gCACA,4BACA,iCACA,+BACA,4BACA,6BACA,+BACA,gCACA,+BACA,kCACA,kCACA,qBACA,gCACA,yBACA,gCACA,gCACA,+BACA,iCACA,yBACA,oBACA,+BACA,gCACA,mCACA,8BACA,mBACA,wBACA,qBACA,4BACA,mCACA,kCACA,mBACA,gCACA,8BACA,+BACA,6BACA,iCACA,+BACA,+BACA,+BACA,iCACA,gCACA,gCACA,iCACA,6BACA,4BACA,6BACA,2BACA,gCACA,6BACA,2BACA,iCACA,+BACA,6BACA,gCACA,6BACA,4BACA,8BACA,6BACA,6BACA,6BACA,6BACA,sBACA,oCACA,sCACA,yCACA,sBACA,0BACA,oCACA,6BACA,oCACA,8BACA,4BACA,2CACA,iCACA,oCACA,qCACA,qCACA,sCACA,qCACA,8BACA,uBACA,oCACA,6BACA,gCACA,0BACA,mCACA,kCACA,6BACA,6BACA,iDACA,kCACA,qCACA,oCACA,mCACA,4BACA"}