{"name": "algotrade-framework", "version": "1.0.0", "description": "A simple CLI application built with Oclif", "main": "dist/index.js", "bin": {"algotrade": "./dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "nodemon --exec ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "cli": "npm run build && node dist/index.js", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "postpack": "rimraf oclif.manifest.json", "prepack": "npm run build && oclif manifest && oclif readme"}, "keywords": ["cli", "oclif", "typescript"], "author": "", "license": "ISC", "oclif": {"bin": "algotrade", "dirname": "algotrade", "commands": "./dist/commands", "plugins": ["@oclif/plugin-help", "@oclif/plugin-plugins"]}, "devDependencies": {"@oclif/test": "^4.1.13", "@types/jest": "^30.0.0", "@types/node": "^24.3.1", "dotenv": "^17.2.2", "jest": "^30.1.3", "nodemon": "^3.1.10", "oclif": "^4.22.16", "rimraf": "^6.0.1", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "dependencies": {"@oclif/core": "^4.5.2", "@oclif/plugin-help": "^6.2.32", "@oclif/plugin-plugins": "^5.4.46", "date-fns-timezone": "^0.1.4", "date-fns-tz": "^3.2.0", "enquirer": "^2.4.1", "kiteconnect": "^5.1.0"}}