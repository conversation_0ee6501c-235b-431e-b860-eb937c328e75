{"name": "get-stdin", "version": "9.0.0", "description": "Get stdin as a string or buffer", "license": "MIT", "repository": "sindresorhus/get-stdin", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava test.js test-buffer.js && echo unicorns | node test-real.js && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["std", "stdin", "stdio", "concat", "buffer", "stream", "process", "read"], "devDependencies": {"@types/node": "^14.14.41", "ava": "^3.15.0", "delay": "^5.0.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}