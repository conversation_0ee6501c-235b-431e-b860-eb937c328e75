import { Connect, KiteConnect, KiteTicker } from "kiteconnect";
import { Ticker } from "kiteconnect/types/ticker";
import { BrokerError } from "../errors";
import {
  CancelOrderParams,
  Exchanges,
  HistoricalDataParams,
  IBroker,
  ModifyOrderParams,
  PlaceOrderParams,
} from "../interface";
import {
  Holding,
  Instrument,
  OHLC,
  OptionChain,
  OptionContract,
  Order,
  Position,
  Quote,
  MarketTiming,
} from "../models";
import { mapTickToQuote, withRetry } from "../utils";

import { toZonedTime } from "date-fns-tz";

export class ZerodhaBroker implements IBroker {
  private kite: Connect;
  private ticker?: Ticker;
  private accessToken?: string;
  private instruments: Instrument[] = [];

  private marketOpen = "09:15";
  private marketClose = "15:30";
  private marketTimezone = "Asia/Calcutta";

  constructor(private apiKey: string, private apiSecret: string, data?: { accessToken?: string; debug?: boolean } | undefined) {
    this.kite = new KiteConnect({ api_key: apiKey, debug: data?.debug, access_token: data?.accessToken });

    if (data?.accessToken) {
      this.accessToken = data?.accessToken;
    }
  }

  public getTiming(): MarketTiming {
    const tz = this.marketTimezone;
    const nowUtc = new Date();

    // current time in exchange timezone
    const now = toZonedTime(nowUtc, tz);

    // today's market start
    const [openH, openM] = this.marketOpen.split(":").map(Number);
    const marketStartTime = new Date(now);
    marketStartTime.setHours(openH!, openM, 0, 0);

    // today's market end
    const [closeH, closeM] = this.marketClose.split(":").map(Number);
    const marketEndTime = new Date(now);
    marketEndTime.setHours(closeH!, closeM, 0, 0);

    return {
      marketOpen: this.marketOpen,
      marketClose: this.marketClose,
      marketStartTime,
      marketEndTime,
      timezone: tz,
      now,
    };
  }

  async init(): Promise<void> {
    // fetch instruments at startup and cache
    this.instruments = await withRetry(async () => {
      const raw = await this.kite.getInstruments();
      return raw.map((i) => ({
        instrumentToken: i.instrument_token,
        symbol: i.tradingsymbol,
        exchange: i.exchange,
        name: i.name,
        segment: i.segment,
        lotSize: i.lot_size,
        tickSize: i.tick_size,
        instrumentType: i.instrument_type,
        expiry: i.expiry ? new Date(i.expiry) : undefined,
        strike: i.strike,
        raw: i,
      } as Instrument));
    });
    console.log("Instruments loaded: ", this.instruments.length);
  }

  getLoginUrl(): string {
    return this.kite.getLoginURL();
  }

  async generateSession(requestToken: string, apiSecret: string): Promise<void> {
    const session = await this.kite.generateSession(requestToken, apiSecret);
    this.kite.setAccessToken(session.access_token);
    this.accessToken = session.access_token;
  }

  setAccessToken(token: string): void {
    this.kite.setAccessToken(token);
    this.accessToken = token;
  }

  getAccessToken(): string | undefined {
    return this.accessToken;
  }

  // ---- Health ----
  async isHealthy(): Promise<boolean> {
    try {
      const profile = await this.getProfile();
      console.log("Profile:", profile);
      return true;
    } catch (e) {
      console.error("Health check failed:", e);
      return false;
    }
  }

  async getProfile(): Promise<{ userId: string; userName: string; broker: string }> {
    return withRetry(async () => {
      const p = await this.kite.getProfile();
      return {
        userId: p.user_id,
        userName: p.user_name,
        broker: "ZERODHA",
      };
    });
  }

  async getInstruments(exchange?: string): Promise<Instrument[]> {
    if (exchange) {
      return this.instruments.filter((i) => i.exchange === exchange);
    }
    return this.instruments;
  }

  async getOptionChain(symbol: string, expiry: Date): Promise<OptionChain> {
    if (this.instruments.length === 0) {
      throw new BrokerError("Instruments not loaded. Call init() first.");
    }

    const expiryDate = expiry.toISOString().split("T")[0]; // "YYYY-MM-DD"

    const contracts = this.instruments
      .filter(
        (i) =>
          i.name === symbol &&
          i.expiry &&
          i.expiry.toISOString().split("T")[0] === expiryDate &&
          ["CE", "PE"].includes(i.instrumentType)
      )
      .map(
        (i): OptionContract => ({
          ...i,
          strike: i.strike || 0,
          expiry: i.expiry!,
          optionType: i.instrumentType as "CE" | "PE",
        })
      );

    return {
      underlying: symbol,
      expiry,
      contracts,
    };
  }

  async getLTP(symbols: string | string[]): Promise<Record<string, Quote>> {
    return withRetry(async () => {
      const data = await this.kite.getLTP(symbols);
      const result: Record<string, Quote> = {};
      for (const [key, q] of Object.entries<any>(data)) {
        result[key] = {
          symbol: q.instrument_token.toString(),
          lastPrice: q.last_price,
          change: 0,
          percentChange: 0,
          raw: q,
        };
      }
      return result;
    });
  }

  async getQuote(symbols: string | string[]): Promise<Record<string, Quote>> {
    return withRetry(async () => {
      const data = await this.kite.getQuote(symbols);
      const result: Record<string, Quote> = {};
      for (const [key, q] of Object.entries<any>(data)) {
        result[key] = {
          symbol: q.instrument_token.toString(),
          lastPrice: q.last_price,
          change: q.net_change,
          percentChange: q.change,
          ohlc: {
            symbol: q.instrument_token.toString(),
            open: q.ohlc.open,
            high: q.ohlc.high,
            low: q.ohlc.low,
            close: q.ohlc.close,
            timestamp: new Date(),
          },
          volume: q.volume,
          raw: q,
        };
      }
      return result;
    });
  }

  async getOHLC(symbol: string | string[]): Promise<OHLC> {
    return withRetry(async () => {
      const data = await this.kite.getOHLC(symbol);
      const q = Object.values<any>(data)[0];
      return {
        symbol: q.instrument_token.toString(),
        open: q.ohlc.open,
        high: q.ohlc.high,
        low: q.ohlc.low,
        close: q.ohlc.close,
        timestamp: new Date(),
      };
    });
  }

  async getHistoricalData(params: HistoricalDataParams): Promise<OHLC[]> {
    return withRetry(async () => {
      // Map interval from your format to KiteConnect format
      const intervalMap: Record<string, string> = {
        "1m": "minute",
        "3m": "3minute",
        "5m": "5minute",
        "10m": "10minute",
        "15m": "15minute",
        "30m": "30minute",
        "60m": "60minute",
        "1d": "day",
        "1w": "day" // 'week' not supported, fallback to 'day'
      };
      const kiteInterval = intervalMap[params.interval] || params.interval;
      const data = await this.kite.getHistoricalData(
        params.instrumentToken,
        kiteInterval as any,
        params.from,
        params.to,
        params.continuous,
        params.oi
      );
      return data.map((d: any): OHLC => ({
        symbol: params.instrumentToken.toString(),
        open: d.open,
        high: d.high,
        low: d.low,
        close: d.close,
        timestamp: new Date(d.date),
      }));
    });
  }

  async placeOrder(params: PlaceOrderParams): Promise<Order> {
    return withRetry(async () => {
      const kiteOrderParams = {
        exchange: params.exchange as Exchanges,
        tradingsymbol: params.tradingsymbol,
        transaction_type: params.transactionType,
        quantity: params.quantity,
        product: params.product,
        order_type: params.orderType,
        price: params.price,
        trigger_price: params.triggerPrice,
        disclosed_quantity: params.disclosedQuantity,
        validity: params.validity,
        tag: params.tag,
      };
      const o = await this.kite.placeOrder(params.variety || "regular", kiteOrderParams);
      return {
        id: o.order_id,
        symbol: params.tradingsymbol,
        exchange: params.exchange,
        quantity: params.quantity,
        filledQuantity: 0,
        price: params.price || 0,
        avgPrice: 0,
        orderType: params.orderType,
        product: params.product,
        status: "OPEN",
        transactionType: params.transactionType,
        placedAt: new Date(),
      };
    });
  }

  async modifyOrder(params: ModifyOrderParams): Promise<Order> {
    return withRetry(async () => {
      const o = await this.kite.modifyOrder(params.variety || "regular", params.orderId, params);
      return {
        id: o.order_id,
        symbol: "",
        exchange: "",
        quantity: params.quantity || 0,
        filledQuantity: 0,
        price: params.price || 0,
        avgPrice: 0,
        orderType: "LIMIT",
        product: "MIS",
        status: "OPEN",
        transactionType: "BUY",
        placedAt: new Date(),
      };
    });
  }

  async cancelOrder(params: CancelOrderParams): Promise<boolean> {
    return withRetry(async () => {
      await this.kite.cancelOrder(params.variety || "regular", params.orderId);
      return true;
    });
  }

  async getOrders(): Promise<Order[]> {
    return withRetry(async () => {
      const orders = await this.kite.getOrders();
      return orders.map((o: any): Order => ({
        id: o.order_id,
        symbol: o.tradingsymbol,
        exchange: o.exchange,
        quantity: o.quantity,
        filledQuantity: o.filled_quantity,
        price: o.price,
        avgPrice: o.average_price,
        orderType: o.order_type,
        product: o.product,
        status: o.status.toUpperCase(),
        transactionType: o.transaction_type,
        placedAt: new Date(o.order_timestamp),
        updatedAt: o.exchange_update_timestamp
          ? new Date(o.exchange_update_timestamp)
          : undefined,
        raw: o,
      }));
    });
  }

  async getOrderHistory(orderId: string): Promise<Order[]> {
    return withRetry(async () => {
      const orders = await this.kite.getOrderHistory(orderId);
      return orders.map((o: any): Order => ({
        id: o.order_id,
        symbol: o.tradingsymbol,
        exchange: o.exchange,
        quantity: o.quantity,
        filledQuantity: o.filled_quantity,
        price: o.price,
        avgPrice: o.average_price,
        orderType: o.order_type,
        product: o.product,
        status: o.status.toUpperCase(),
        transactionType: o.transaction_type,
        placedAt: new Date(o.order_timestamp),
        updatedAt: o.exchange_update_timestamp
          ? new Date(o.exchange_update_timestamp)
          : undefined,
        raw: o,
      }));
    });
  }

  async getPositions(): Promise<Position[]> {
    return withRetry(async () => {
      const data = await this.kite.getPositions();
      return data.net.map((p: any): Position => ({
        symbol: p.tradingsymbol,
        exchange: p.exchange,
        quantity: p.quantity,
        avgPrice: p.average_price,
        pnl: p.pnl,
        dayPnl: p.day_pnl,
        product: p.product,
        raw: p,
      }));
    });
  }

  async getHoldings(): Promise<Holding[]> {
    return withRetry(async () => {
      const data = await this.kite.getHoldings();
      return data.map((h: any): Holding => ({
        symbol: h.tradingsymbol,
        exchange: h.exchange,
        quantity: h.quantity,
        avgPrice: h.average_price,
        lastPrice: h.last_price,
        pnl: h.pnl,
        raw: h,
      }));
    });
  }

  // ---- 🔥 Ticker ----
  async connectTicker({
    onTicks,
    onConnect,
    onDisconnect,
    onError,
    reconnect = true,
  }: {
    onTicks: (ticks: Quote[]) => void,
    onConnect?: () => void,
    onDisconnect?: () => void,
    onError?: (err: any) => void
    reconnect?: boolean | undefined,
  }): Promise<void> {
    if (!this.accessToken) {
      throw new BrokerError("Access token required before connecting ticker");
    }

    this.ticker = new KiteTicker({
      api_key: this.apiKey,
      access_token: this.accessToken,
      reconnect: reconnect,
    });

    this.ticker.autoReconnect(reconnect, 5, 5); // try 5 times with 5s gap

    this.ticker.on("connect", () => {
      onConnect?.();
    });

    this.ticker.on("disconnect", () => {
      onDisconnect?.();
    });

    this.ticker.on("ticks", (ticks: any[]) => {
      const quotes = ticks.map(mapTickToQuote);
      onTicks(quotes);
    });

    this.ticker.on("error", (err: any) => {
      console.error("Ticker error:", err);
    });

    this.ticker.connect();
  }

  disconnectTicker(): void {
    this.ticker?.disconnect();
  }

  subscribeTicks(symbols: number[], cb?: (ticks: Quote[]) => void): void {
    if (!this.ticker) throw new BrokerError("Ticker not connected");
    this.ticker.subscribe(symbols);
    if (cb) {
      this.ticker.on("ticks", (ticks: any[]) => {
        cb(ticks.map(mapTickToQuote));
      });
    }
  }

  unsubscribeTicks(symbols: number[]): void {
    if (!this.ticker) throw new BrokerError("Ticker not connected");
    this.ticker.unsubscribe(symbols);
  }
}
