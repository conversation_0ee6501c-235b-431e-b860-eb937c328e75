{"name": "detect-newline", "version": "4.0.1", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": "sindresorhus/detect-newline", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}}