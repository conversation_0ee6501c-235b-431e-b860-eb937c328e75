<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-view</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-view----1093">
    <span>npm-view</span>
    <span class="version">@10.9.3</span>
</h1>
<span class="description">View registry info</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#json"><code>json</code></a></li><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li></ul><li><a href="#output">Output</a></li><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm view [&lt;package-spec&gt;] [&lt;field&gt;[.subfield]...]

aliases: info, show, v
</code></pre>
<h3 id="description">Description</h3>
<p>This command shows data about a package and prints it to stdout.</p>
<p>As an example, to view information about the <code>connect</code> package from the registry, you would run:</p>
<pre><code class="language-bash">npm view connect
</code></pre>
<p>The default version is <code>"latest"</code> if unspecified.</p>
<p>Field names can be specified after the package descriptor.
For example, to show the dependencies of the <code>ronn</code> package at version
<code>0.3.5</code>, you could do the following:</p>
<pre><code class="language-bash">npm view ronn@0.3.5 dependencies
</code></pre>
<p>By default, <code>npm view</code> shows data about the current project context (by looking for a <code>package.json</code>).
To show field data for the current project use a file path (i.e. <code>.</code>):</p>
<pre><code class="language-bash">npm view . dependencies
</code></pre>
<p>You can view child fields by separating them with a period.
To view the git repository URL for the latest version of <code>npm</code>, you would run the following command:</p>
<pre><code class="language-bash">npm view npm repository.url
</code></pre>
<p>This makes it easy to view information about a dependency with a bit of
shell scripting. For example, to view all the data about the version of
<code>opts</code> that <code>ronn</code> depends on, you could write the following:</p>
<pre><code class="language-bash">npm view opts@$(npm view ronn dependencies.opts)
</code></pre>
<p>For fields that are arrays, requesting a non-numeric field will return
all of the values from the objects in the list. For example, to get all
the contributor email addresses for the <code>express</code> package, you would run:</p>
<pre><code class="language-bash">npm view express contributors.email
</code></pre>
<p>You may also use numeric indices in square braces to specifically select
an item in an array field. To just get the email address of the first
contributor in the list, you can run:</p>
<pre><code class="language-bash">npm view express contributors[0].email
</code></pre>
<p>If the field value you are querying for is a property of an object, you should run:</p>
<pre><code class="language-bash">npm view express time'[4.8.0]'
</code></pre>
<p>Multiple fields may be specified, and will be printed one after another.
For example, to get all the contributor names and email addresses, you
can do this:</p>
<pre><code class="language-bash">npm view express contributors.name contributors.email
</code></pre>
<p>"Person" fields are shown as a string if they would be shown as an
object.  So, for example, this will show the list of <code>npm</code> contributors in
the shortened string format.  (See <a href="../configuring-npm/package-json.html"><code>package.json</code></a> for more on this.)</p>
<pre><code class="language-bash">npm view npm contributors
</code></pre>
<p>If a version range is provided, then data will be printed for every
matching version of the package.  This will show which version of <code>jsdom</code>
was required by each matching version of <code>yui3</code>:</p>
<pre><code class="language-bash">npm view yui3@'&gt;0.5.4' dependencies.jsdom
</code></pre>
<p>To show the <code>connect</code> package version history, you can do
this:</p>
<pre><code class="language-bash">npm view connect versions
</code></pre>
<h3 id="configuration">Configuration</h3>
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h3 id="output">Output</h3>
<p>If only a single string field for a single version is output, then it
will not be colorized or quoted, to enable piping the output to
another command. If the field is an object, it will be output as a JavaScript object literal.</p>
<p>If the <code>--json</code> flag is given, the outputted fields will be JSON.</p>
<p>If the version range matches multiple versions then each printed value
will be prefixed with the version it applies to.</p>
<p>If multiple fields are requested, then each of them is prefixed with
the field name.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../using-npm/package-spec.html">package spec</a></li>
<li><a href="../commands/npm-search.html">npm search</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../commands/npm-docs.html">npm docs</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-view.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>