{"name": "timezone-support", "version": "1.8.1", "description": "Lightweight time zone support for your applications or other date libraries.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://prantl.tk"}, "contributors": ["CrisColamb <and<PERSON><PERSON>.didki<PERSON>@infopulse.com>"], "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/prantlf/timezone-support/blob/master/LICENSE"}], "homepage": "https://github.com/prantlf/timezone-support#readme", "repository": {"type": "git", "url": "https://github.com/prantlf/timezone-support.git"}, "bugs": {"url": "https://github.com/prantlf/timezone-support/issues"}, "engines": {"node": ">=6"}, "bin": {"create-timezone-data": "./bin/create-timezone-data"}, "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["bin", "dist", "src", "util"], "scripts": {"prepare": "npm run build", "lint": "run-s lint:js lint:ts", "lint:fix": "standard --verbose --fix bin/create-timezone-data perf/*.js 'src/**/*.js' test/*.js util/*.js", "lint:js": "standard --verbose bin/create-timezone-data perf/*.js 'src/**/*.js' test/*.js util/*.js", "lint:ts": "tslint -t stylish src/*.ts src/lookup/*.ts test/*.ts", "generate": "bin/create-timezone-data -ma -o src/lookup/data.js && bin/create-timezone-data -m 2012 2022 -o src/lookup/data-2012-2022.js && bin/create-timezone-data -m 1900 2050 -o src/lookup/data-1900-2050.js && bin/create-timezone-data -m 1970 2038 -o src/lookup/data-1970-2038.js", "generate:browser-tests": "node util/generate-browser-tests", "compile": "rollup -c", "copy:typings": "cpy src/index.d.ts src --rename=index-2012-2022.d.ts && cpy src/index.d.ts src --rename=index-1970-2038.d.ts && cpy src/index.d.ts src --rename=index-1900-2050.d.ts && cpy 'src/*.d.ts' dist && cpy src/lookup/data.d.ts src/lookup --rename=data-2012-2022.d.ts && cpy src/lookup/data.d.ts src/lookup --rename=data-1970-2038.d.ts && cpy src/lookup/data.d.ts src/lookup --rename=data-1900-2050.d.ts && cpy 'src/lookup/*.d.ts' dist", "build": "run-s lint generate compile copy:typings", "compile:tests": "tsc --lib es6 test/typings.test.ts", "check": "jest --testPathIgnorePatterns \"browser.test.js\" --collectCoverage", "check:browser": "jest --testPathPattern browser.test.js", "benchmark": "node perf", "coverage": "test `node --version | cut -c 2` -eq 8 && cat coverage/lcov.info | coveralls", "test": "run-s compile:tests check generate:browser-tests check:browser", "travis-deploy-once": "travis-deploy-once", "semantic-release": "semantic-release"}, "standard": {"ignore": ["src/lookup/data*.js"]}, "jest": {"roots": ["test"], "coverageDirectory": "coverage", "collectCoverageFrom": ["src/!(lookup-convert).js"], "coverageReporters": ["lcov"], "coverageThreshold": {"global": {"statements": 100, "branches": 100, "functions": 100, "lines": 100}}}, "dependencies": {"commander": "2.19.0"}, "devDependencies": {"@babel/core": "7.1.6", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "benchmark": "2.1.4", "connect": "3.6.6", "coveralls": "3.0.2", "cpy-cli": "2.0.0", "es6-promisify": "6.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "8.0.0", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "fast-glob": "2.2.4", "fs-extra": "7.0.1", "jasmine-core": "3.3.0", "jest": "23.6.0", "moment-timezone": "0.5.23", "npm-run-all": "4.1.5", "puppeteer": "1.10.0", "regenerator-runtime": "0.13.1", "rimraf": "2.6.2", "rollup": "0.67.3", "rollup-plugin-babel": "4.0.3", "rollup-plugin-clean": "1.0.0", "rollup-plugin-uglify": "6.0.0", "semantic-release": "15.12.2", "serve-static": "1.13.2", "standard": "12.0.1", "travis-deploy-once": "5.0.9", "tslint": "5.11.0", "tslint-config-standard": "8.0.1", "typescript": "3.1.6"}, "keywords": ["timezone", "tzdata", "convert", "offset", "date", "time"]}