# detect-newline

> Detect the dominant newline character of a string

## Install

```sh
npm install detect-newline
```

## Usage

```js
import {detectNewline} from 'detect-newline';

detectNewline('foo\nbar\nbaz\r\n');
//=> '\n'
```

## API

### detectNewline(string)

Returns the detected newline or `undefined` when no newline character is found or `\n` when no dominant newline is present.

### detectNewlineGraceful(unknown)

Returns the detected newline or `\n` when no newline character is found, no dominant newline is present, or the input is not a string.

## Related

- [detect-newline-cli](https://github.com/sindresorhus/detect-newline-cli) - CLI for this module
- [detect-indent](https://github.com/sindresorhus/detect-indent) - Detect the indentation of code
